package role_cmm

import (
	"chaossrv/internal/robot"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
)

// InitGlobal 初始化Global请求
func InitGlobal(rb *robot.Robot) {
	// 世界相关
	c := rb.Client
	s := &GlobalRoleScript{
		rb,
	}

	// 房间信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_UPDATE_ITEM_NTF, &hallPB.UpdateItemInfoNtf{}, s.UpdateItemInfoNtf)
}
