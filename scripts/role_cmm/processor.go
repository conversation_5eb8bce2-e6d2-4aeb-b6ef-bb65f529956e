package role_cmm

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"github.com/sirupsen/logrus"
)

// GlobalRoleScript Global
type GlobalRoleScript struct {
	TargetRobot *robot.Robot
}

// UpdateItemInfoNtf 2014 更新物品推送
func (h *GlobalRoleScript) UpdateItemInfoNtf(iRsp socket.IProto) error {

	// 返回
	rsp := iRsp.(*hallPB.UpdateItemInfoNtf)

	logrus.Infof("GetRoomInfoHandler, robot:%d, rewards:%v", h.TargetRobot.UID, rsp)

	return nil
}
