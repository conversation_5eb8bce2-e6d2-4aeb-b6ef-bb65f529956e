package role_world

import (
	"chaossrv/internal/robot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	worldPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/world"
)

func InitWorld(rb *robot.Robot) {
	c := rb.Client
	s := &WorldRoleScript{
		rb,
	}

	c.HandlerCMD(commonPB.MsgID_CMD_WORLD_GET_WEATHER_RSP, &worldPB.GetWeatherRsp{}, s.GetWeatherRsp)

	c.<PERSON>lerCMD(commonPB.MsgID_CMD_WORLD_GET_WORLD_TIME_RSP, &worldPB.GetWorldTimeRsp{}, s.GetWorldTimeRsp)

}
