package role_world

import (
	"chaossrv/internal/robot"
	"time"

	mockWorld "chaossrv/mocktables/world"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

func WorldPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	safego.Go(func() {
		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_WORLD_GET_WEATHER_REQ), mockWorld.GetWeatherReq())

		for {
			rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_WORLD_GET_WORLD_TIME_REQ), mockWorld.GetWorldTimeReq())
			time.Sleep(1 * time.Second)
		}
	})

}
