package role_world

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"

	worldPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/world"
	"github.com/sirupsen/logrus"
)

type WorldRoleScript struct {
	TargetRobot *robot.Robot
}

func (s *WorldRoleScript) GetWeatherRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*worldPB.GetWeatherRsp)
	if rsp == nil {
		logrus.Errorf("robot:%d, Faild rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}
	logrus.Debugf("robot:%d, [GetWeatherRsp]:%s", s.TargetRobot.UID, rsp.String())

	return nil
}

func (s *WorldRoleScript) GetWorldTimeRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*worldPB.GetWorldTimeRsp)
	if rsp == nil {
		logrus.Errorf("robot:%d, Faild rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}
	logrus.Debugf("robot:%d, [GetWorldTime]:%s", s.TargetRobot.UID, rsp.String())

	return nil
}
