package role_rank

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	mockRank "chaossrv/mocktables/rank"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func RankPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	time.AfterFunc(config.TimeCommWait, func() {
		rb.Report.EnterTripTimeS = time.Now().UnixMilli()

		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_RANK_GET_RANK_LIST_REQ), mockRank.GetRankListMock(201510004))
	})

}
