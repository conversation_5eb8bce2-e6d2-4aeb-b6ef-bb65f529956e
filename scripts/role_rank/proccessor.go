package role_rank

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"

	rankPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/rank"
	"github.com/sirupsen/logrus"
)

type RankRoleScript struct {
	TargetRobot *robot.Robot
}

func (s *RankRoleScript) GetRankInfo(iRsp socket.IProto) error {
	rsp := iRsp.(*rankPB.GetRankListRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, GetRankInfoRsp rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, GetRankInfoRsp:%v", s.TargetRobot.UID, rsp)
	return nil
}
