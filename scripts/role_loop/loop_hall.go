package role_loop

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	hallData "chaossrv/internal/robot/hall_data"
	mockHall "chaossrv/mocktables/hall"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
)

// LoopHallReq 大厅请求
func LoopHallReq(rb *robot.Robot) {
	if rb == nil {
		return
	}

	//role_hall.InitHall(rb)

	// 下面这些是必须要发的 后面的接口 可以自己抽出 
	rb.ExecuteType = config.ExecuteTypeStress
	rb.HallData = &hallData.HallData{}
	//LoopNextHallReq(rb, int32(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ), false)
	// 直接请求房间信息
	LoopNextHallReq(rb, int32(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ), false)
}

func LoopNextHallReq(rb *robot.Robot, msgId int32, exit bool) bool  {
	if rb == nil {
		logrus.Errorf("load hall failed, robot is nil")
		return false
	}

	if rb.ExecuteType != config.ExecuteTypeStress {
		return false
	}

	if exit {
		logrus.Debugf("robot:%d, exit hall", rb.UID)
	
		// 钓点服请求
		LoopHookFish(rb)

		return true
	}

	var msg proto.Message = nil

	switch msgId {
	case int32(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ):
		msg = mockHall.FirstEnterHallReq()
	case int32(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_REQ):
		msg = mockHall.GetRodRigInfoReq()
	case int32(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_REQ):
		msg = mockHall.GetItemInfoByType()
	case int32(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ):
		msg = mockHall.GetTripRodReq(rb.UID)
	case int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ):
		var rigId int32 = 1 
		if rb.HallData != nil && len(rb.HallData.RodRigInfo) > 0 {
			rigId = rb.HallData.RodRigInfo[0].GetRigId()
		}
		msg = mockHall.LoadTripRodReq(rigId, 1)
	case int32(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ):
		msg = mockHall.UpdateRodRigInfoReq()
	case int32(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ):
		msg = mockHall.GetRoomInfoReq()
	// 触发加道具
	case int32(commonPB.MsgID_CMD_STORE_BUY_REQ):
		msgId, msg = GetRbAddItemInfo(rb)
	case int32(commonPB.MsgID_CMD_ENTER_FISHERY_REQ):
		AddItem(rb, []*commonPB.ItemBase{
			{ItemId: 101, ItemCount: 10000},
		})
		msg = mockHall.EnterFisheryReq()
	default:
		logrus.Errorf("role_loop.LoopNextHallReq: robot:%d unknown msgId %d", rb.UID, msgId)
	}

	logrus.Debugf("role_loop.LoopNextHallReq: robot:%d, msgId:%s", rb.UID, commonPB.MsgID_name[msgId])

	if msg != nil {
		rb.Client.SendPbMsg(int(msgId), msg)
	}

	return false
}