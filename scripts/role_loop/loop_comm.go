package role_loop

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	mockHall "chaossrv/mocktables/hall"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/golang/protobuf/proto"
)

// GetRbAddItemInfo 获取机器人添加物品信息
func GetRbAddItemInfo(rb *robot.Robot) (int32, proto.Message) {
	if rb == nil || rb.HallData == nil {
		return 0, nil
	}

	itemList := make([]*commonPB.ItemBase, 0)
	var msgId int32
	var msgBody proto.Message


	switch rb.HallData.AddItemReason {
	case config.BuyReasonBagRig:
		itemList = GetBagRigItemList(rb)
		msgId = int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ)
		msgBody = mockHall.LoadTripRodReq(1, 1)
	// case config.BuyReasonPondCost:
	// 	itemList = GetEnterPondCostItem(rb)
	// 	msgId = int32(commonPB.MsgID_CMD_ENTER_FISHERY_REQ)
	// 	msgBody = mockHall.EnterFisheryReq()
	default:
	}

	AddItem(rb, itemList)

	return msgId, msgBody
}

// GetEnterPondCostItem 进入钓场消耗物品
func GetEnterPondCostItem(rb * robot.Robot) []*commonPB.ItemBase {
	itemList := []*commonPB.ItemBase {
		{
			ItemId:    101,
			ItemCount: 1000,
		},
		{
			ItemId:    102,
			ItemCount: 1000,
		},
	}

	return itemList
}

func GetBagRigItemList(rb *robot.Robot) []*commonPB.ItemBase {
	if rb == nil || rb.HallData == nil || rb.HallData.BagRigInfo == nil {
		return nil
	}

	itemList := make([]*commonPB.ItemBase, 0)
	for _, itemInfo := range rb.HallData.BagRigInfo.GetInfo() {
		if itemInfo.GetInstanceId() == "" {
			itemList = append(itemList, &commonPB.ItemBase{ItemId: itemInfo.GetItemId(), ItemCount: 1})
		}
	}

	return itemList
}

func AddItem(rb *robot.Robot, itemList []*commonPB.ItemBase) {
	if rb == nil || len(itemList) <= 0 {
		return
	}

	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(1),
		interceptor.WithPlayerId(rb.UID),
		interceptor.WithClientID(1001))

	item_kit.SendReward(ctx, rb.UID, itemList, commonPB.ITEM_SOURCE_TYPE_IST_GM, true)
}
