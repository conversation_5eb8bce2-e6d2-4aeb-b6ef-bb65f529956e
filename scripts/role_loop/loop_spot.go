package role_loop

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	spotData "chaossrv/internal/robot/spot_data"
	"time"

	mockSpot "chaossrv/mocktables/spot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
)

// LoopHookFish 连续钓鱼请求
func LoopHookFish(rb *robot.Robot) {
	if rb == nil {
		return
	}

	// role_spot.InitSpot(rb)
	rb.ExecuteType = config.ExecuteTypeStress

	// 判断是否已经进入钓场
	if rb.HallData == nil || rb.HallData.RoomInfo == nil {
		logrus.Errorf("robot:%d hallData:%+v or roomInfo:%+v is nil", rb.UID, rb.HallData, rb.HallData.RoomInfo)
		return
	}

	rb.SpotData = &spotData.SpotData{}

	LoopHookReq(rb, int32(commonPB.MsgID_CMD_STORE_BUY_REQ))
	LoopHookReq(rb, int32(commonPB.MsgID_CMD_GET_SPOT_SCENE_REQ))
}

// LoopHookReq 发送中鱼下个请求
func LoopHookReq(rb *robot.Robot, msgId int32) {
	if rb == nil || rb.ExecuteType != config.ExecuteTypeStress {
		return
	}

	var msg proto.Message = nil

	switch msgId {
	case int32(commonPB.MsgID_CMD_GET_SPOT_SCENE_REQ):
		msg = mockSpot.GetSpotSceneReq(rb.HallData)
	// 抛竿请求
	case int32(commonPB.MsgID_CMD_THROW_ROD_REQ):
		msg = mockSpot.ThrowRodReq()
	// 加道具请求
	case int32(commonPB.MsgID_CMD_STORE_BUY_REQ):
		AddItem(rb, []*commonPB.ItemBase{
			{ItemId: 2020005, ItemCount: 1000},
		})
	// 使用道具请求
	case int32(commonPB.MsgID_CMD_USE_ITEM_REQ):
		AddItem(rb, []*commonPB.ItemBase{
			{ItemId: 2020005, ItemCount: 3},
		})
		msg = mockSpot.UseFoodItemReq()
	// 中鱼请求
	case int32(commonPB.MsgID_CMD_FISH_HOOK_REQ):
		time.Sleep(time.Duration(rb.SpotData.HookInterval) * time.Millisecond)
		msg = mockSpot.FishHookReq()
	// 搏鱼请求
	case int32(commonPB.MsgID_CMD_FISH_BATTLE_FISH_REQ):
		time.Sleep(config.TimeCommWait)
		msg = mockSpot.FishBattleReq()
	// 收竿请求
	case int32(commonPB.MsgID_CMD_CATCH_ROD_REQ):
		msg = mockSpot.CatchRodReq()
	// 鱼入护请求
	case int32(commonPB.MsgID_CMD_FISH_ENTRY_OPT_REQ):
		msg = mockSpot.FishEntryOptReq(rb.SpotData.HookFishInstance)
	// 离开房间
	case int32(commonPB.MsgID_CMD_EXIT_ROOM_REQ):
		msg = mockSpot.ExitRoomReq()
	case int32(commonPB.MsgID_CMD_SPOT_HOOK_START_REQ):
		msg = mockSpot.HookStartReq()
	default:
		logrus.Errorf("robot:%d msgId:%d is not support", rb.UID, msgId)
	}

	if msg != nil {
		rb.Client.SendPbMsg(int(msgId), msg)
	}

	logrus.Debugf("LoopHookReq: robot:%d, msgId:%s", rb.UID, commonPB.MsgID_name[msgId])
}	
