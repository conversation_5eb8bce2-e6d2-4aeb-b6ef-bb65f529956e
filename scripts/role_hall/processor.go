package role_hall

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"chaossrv/scripts/role_loop"
	"fmt"
	"time"

	mailPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/msg"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"github.com/sirupsen/logrus"
)

// hallRoleScript
type HallRoleScript struct {
	TargetRobot *robot.Robot
}

// GetRoomInfo 请求房间信息返回
func (h *HallRoleScript) GetRoomInfo(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetRoomInfoRsp)
	if rsp == nil || rsp.GetRoomInfo() == nil {
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_ENTER_FISHERY_REQ), false)
		logrus.Debugf("robot:%d, not in room", h.TargetRobot.UID)
		return nil
	}

	// 已经在房间中
	h.TargetRobot.HallData.RoomInfo = rsp.GetRoomInfo()
	role_loop.LoopNextHallReq(h.TargetRobot, 0, true)

	logrus.Infof("GetRoomInfoHandler, robot:%d, roomInfo:%s", h.TargetRobot.UID, rsp.GetRoomInfo().String()) 

	return nil
}

// EnterFisheryRsp 进入钓点返回
func (h *HallRoleScript) EnterFisheryRsp(iRsp socket.IProto) error {

	startTimeS := time.Now().UnixMilli()

	// 返回
	rsp := iRsp.(*hallPB.EnterFisheryRsp)

	// 道具不足 加钱
	if rsp.Ret.Code == commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH {
		h.TargetRobot.HallData.AddItemReason = config.BuyReasonPondCost
		// 购买竿组信息
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_STORE_BUY_REQ), false)
	}

	if rsp.GetRoomInfo() == nil {
		logrus.Errorf("robot:%d, roomId is nil", h.TargetRobot.UID)
		return fmt.Errorf("roomId is nil")
	}

	roomId := rsp.GetRoomInfo().GetRoomId()
	endTimeS := time.Now().UnixMilli()

	h.TargetRobot.HallData.RoomInfo = rsp.GetRoomInfo()
	role_loop.LoopNextHallReq(h.TargetRobot, 0, true)

	logrus.Infof("robot:%d, roomId:%s, useTime:%d", h.TargetRobot.UID, roomId, endTimeS-startTimeS)

	return nil
}

// GetItemInfoRsp 查询道具id列表返回
func (h *HallRoleScript) GetItemInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetItemInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("GetItemInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("GetItemInfoRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("GetItemInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetItemInfoByTypeRsp 根据类型查询道具信息返回
func (h *HallRoleScript) GetItemInfoByTypeRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetItemInfoByTypeRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("GetItemInfoByTypeRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("GetItemInfoByTypeRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("GetItemInfoByTypeRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetGoodsBuyInfoRsp 商品购买信息返回
func (h *HallRoleScript) GetGoodsBuyInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetGoodsBuyInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("GetGoodsBuyInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("GetGoodsBuyInfoRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("GetGoodsBuyInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// StoreBuyRsp 商城购买返回
func (h *HallRoleScript) StoreBuyRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.StoreBuyRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("StoreBuyRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("StoreBuyRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("StoreBuyRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetLastGameInfoRsp 上次游戏信息返回
func (h *HallRoleScript) GetLastGameInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetLastGameInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("GetLastGameInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("GetLastGameInfoRsp, errCode:%d", rsp.Ret.Code)
	}
	logrus.Infof("GetLastGameInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetPlayerInfoRsp 玩家游戏信息
func (h *HallRoleScript) GetPlayerInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetPlayerInfoRsp)
	if rsp == nil {
		logrus.Errorf("GetPlayerInfoRsp, robot:%d, rsp is nil", h.TargetRobot.UID)
		return fmt.Errorf("GetPlayerInfoRsp, rsp is nil")
	}

	logrus.Infof("GetPlayerInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// UseItemRsp 消耗道具返回
func (h *HallRoleScript) UseItemRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.UseItemRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("UseItemRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("UseItemRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("UseItemRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// FirstEnterHallRsp 进入大厅返回
func (h *HallRoleScript) FirstEnterHallRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.FirstEnterHallRsp)
	if rsp == nil {
		logrus.Errorf("FirstEnterHallRsp, robot:%d, rsp is nil", h.TargetRobot.UID)
		return fmt.Errorf("FirstEnterHallRsp,  rsp is nil")
	}

	// 查询竿组信息
	//role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_REQ), false)
	// 查询钓场房间信息(判断是否进入钓场)
	role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ), false)

	logrus.Infof("FirstEnterHallRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// UpdateRodRigInfoRsp 更新竿架信息返回
func (h *HallRoleScript) UpdateRodRigInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.UpdateRodRigInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("UpdateRodRigInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("UpdateRodRigInfoRsp, errCode:%d", rsp.Ret.Code)
	}

	// 背包数据
	role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ), false)

	logrus.Infof("UpdateRodRigInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetRodRigInfoRsp 查询竿架信息返回
func (h *HallRoleScript) GetRodRigInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetRodRigInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("GetRodRigInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("GetRodRigInfoRsp, errCode:%d", rsp.Ret.Code)
	}

	// 竿组数据
	if len(rsp.GetRigList()) <= 0 {
		// 更新竿组信息
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ), false)
	} else {
		h.TargetRobot.HallData.RodRigInfo = rsp.GetRigList()
		// 查询背包竿组
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ), false)
	}

	logrus.Infof("GetRodRigInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)

	return nil
}

// DeleteRodRigInfoRsp 删除竿架信息返回
func (h *HallRoleScript) DeleteRodRigInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.DeleteRodRigInfoRsp)
	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("DeleteRodRigInfoRsp, robot:%d, errCode:%d", h.TargetRobot.UID, rsp.Ret.Code)
		return fmt.Errorf("DeleteRodRigInfoRsp, errCode:%d", rsp.Ret.Code)
	}

	logrus.Infof("DeleteRodRigInfoRsp, robot:%d, rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetStatsListRsp 获取统计列表返回
func (h *HallRoleScript) GetStatsList(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetStatListRsp)
	if rsp == nil {
		return fmt.Errorf("GetStatList, rsp is nil")
	}
	logrus.Infof("GetStatsList uid:%+v rsp:%v", h.TargetRobot.UID, rsp)
	return nil
}

// GetTripBagRsp 获取旅行背包
func (h *HallRoleScript) GetTripBagRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.GetTripBagRsp)
	if rsp == nil {
		return fmt.Errorf("GetTripBag Rsp is null")
	}
	logrus.Infof("GetTripBagRsp:%+v", rsp)
	return nil
}

// ModifyTripBagRsp 修改旅行背包
func (h *HallRoleScript) ModifyTripBagRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*hallPB.ModifyTripBagRsp)
	if rsp == nil {
		return fmt.Errorf("ModifyTripBag Rsp is null")
	}
	logrus.Infof("Modify TripBag%+v", rsp)
	return nil
}

// GetTripRodReq 获取旅行干架信息
func (h *HallRoleScript) GetTripRodRsp(iReq socket.IProto) error {
	rsp := iReq.(*hallPB.GetTripRodRsp)
	if rsp == nil {
		return fmt.Errorf("GetTripRod Rsp is null")
	}

	if len(rsp.GetList()) > 0 {
		// 查询钓场信息
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ), false)
	} else {
		// 加载背包竿架信息
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ), false)
	}

	logrus.Infof("GetTripRodRsp:%+v", rsp)
	return nil
}

func (h *HallRoleScript) LoadTripRodRsp(iReq socket.IProto) error {
	rsp := iReq.(*hallPB.LoadTripRodRsp)
	if rsp == nil {
		return fmt.Errorf("LoadTripRod Rsp is null")
	}

	if rsp.GetInfo() != nil {
		h.TargetRobot.HallData.AddItemReason = config.BuyReasonBagRig
		h.TargetRobot.HallData.BagRigInfo = rsp.GetInfo()
		// 购买竿组信息
		role_loop.LoopNextHallReq(h.TargetRobot, int32(commonPB.MsgID_CMD_STORE_BUY_REQ), false)
	}

	logrus.Infof("LoadTripRodRsp:%+v", rsp)
	return nil
}

func (h *HallRoleScript) DelTripRodRsp(iReq socket.IProto) error {
	rsp := iReq.(*hallPB.DelTripRodRsp)
	if rsp == nil {
		return fmt.Errorf("DelTripRod Rsp is null")
	}
	logrus.Infof("DelTripRodRsp:%+v", rsp)
	return nil
}

func (h *HallRoleScript) RealNameAuthRsp(iReq socket.IProto) error {
	rsp := iReq.(*hallPB.RealNameAuthRsp)
	if rsp == nil {
		return fmt.Errorf("realNameRsp is null")
	}
	logrus.Infof("RealNameRsp:%+v", rsp)
	return nil
}

func (h *HallRoleScript) MsgBroadcast(iReq socket.IProto) error {
	rsp := iReq.(*mailPB.MsgBroadcastNtf)
	if rsp == nil {
		return fmt.Errorf("MsgBroadcastNtf is null")
	}
	logrus.Infof("MsgBroadcastNtf:%+v", rsp)
	return nil
}

// UpdateGuideProgressRsp 新手引导进度更新返回
func (h *HallRoleScript) UpdateGuideProgressRsp(iReq socket.IProto) error {
	rsp := iReq.(*hallPB.UpdateGuideProgressRsp)
	if rsp == nil {
		return fmt.Errorf("UpdateGuideProgressRsp is null")
	}
	logrus.Infof("UpdateGuideProgressRsp:%+v", rsp)
	return nil
}
