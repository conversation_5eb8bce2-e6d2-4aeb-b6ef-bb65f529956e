package role_hall

import (
	"chaossrv/internal/robot"

	mailPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/msg"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
)

// InitHall 初始化世界服请求
func InitHall(rb *robot.Robot) {
	// 世界相关
	c := rb.Client
	s := &HallRoleScript{
		rb,
	}

	// 房间信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_ROOM_INFO_RSP, &hallPB.GetRoomInfoRsp{}, s.GetRoomInfo)

	// 进入钓点返回
	c.HandlerCMD(commonPB.MsgID_CMD_ENTER_FISHERY_RSP, &hallPB.EnterFisheryRsp{}, s.EnterFisheryRsp)

	// 查询道具信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_ITEM_INFO_RSP, &hallPB.GetItemInfoRsp{}, s.GetItemInfoRsp)

	// 根据类型查询道具类型返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_RSP, &hallPB.GetItemInfoByTypeRsp{}, s.GetItemInfoByTypeRsp)

	// 查询商品购买信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_GOODS_BUY_INFO_RSP, &hallPB.GetGoodsBuyInfoRsp{}, s.GetGoodsBuyInfoRsp)

	// 购买商品返回
	c.HandlerCMD(commonPB.MsgID_CMD_STORE_BUY_RSP, &hallPB.StoreBuyRsp{}, s.StoreBuyRsp)

	// 上次游戏信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_LAST_GAME_INFO_RSP, &hallPB.GetLastGameInfoRsp{}, s.GetLastGameInfoRsp)

	// 玩家信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_PLAYER_INFO_RSP, &hallPB.GetPlayerInfoRsp{}, s.GetPlayerInfoRsp)

	// 使用物品返回
	c.HandlerCMD(commonPB.MsgID_CMD_USE_ITEM_RSP, &hallPB.UseItemRsp{}, s.UseItemRsp)

	// 首次进入大厅返回
	c.HandlerCMD(commonPB.MsgID_CMD_FIRST_ENTER_HALL_RSP, &hallPB.FirstEnterHallRsp{}, s.FirstEnterHallRsp)

	// 更新竿架信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_RSP, &hallPB.UpdateRodRigInfoRsp{}, s.UpdateRodRigInfoRsp)

	// 查询竿架信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_RSP, &hallPB.GetRodRigInfoRsp{}, s.GetRodRigInfoRsp)

	// 删除竿架信息返回
	c.HandlerCMD(commonPB.MsgID_CMD_DELETE_ROD_RIG_INFO_RSP, &hallPB.DeleteRodRigInfoRsp{}, s.DeleteRodRigInfoRsp)

	// 获取统计返回
	c.HandlerCMD(commonPB.MsgID_CMD_GET_STAT_LIST_RSP, &hallPB.GetStatListRsp{}, s.GetStatsList)

	// 查询旅行背包
	c.HandlerCMD(commonPB.MsgID_CMD_GET_TRIP_BAG_RSP, &hallPB.GetTripBagRsp{}, s.GetTripBagRsp)

	// 修改旅行背包
	c.HandlerCMD(commonPB.MsgID_CMD_MODIFY_TRIP_BAG_RSP, &hallPB.ModifyTripBagRsp{}, s.ModifyTripBagRsp)

	// 查询旅行钓竿
	c.HandlerCMD(commonPB.MsgID_CMD_GET_TRIP_ROD_RSP, &hallPB.GetTripRodRsp{}, s.GetTripRodRsp)

	// 加载旅行钓竿
	c.HandlerCMD(commonPB.MsgID_CMD_LOAD_TRIP_ROD_RSP, &hallPB.LoadTripRodRsp{}, s.LoadTripRodRsp)

	// 删除旅行钓竿
	c.HandlerCMD(commonPB.MsgID_CMD_DEL_TRIP_ROD_RSP, &hallPB.DelTripRodRsp{}, s.DelTripRodRsp)

	// 查询旅行钓点
	c.HandlerCMD(commonPB.MsgID_CMD_REAL_NAME_AUTH_RSP, &hallPB.RealNameAuthRsp{}, s.RealNameAuthRsp)

	// 查询旅行钓点
	c.HandlerCMD(commonPB.MsgID_CMD_MSG_BROADCAST, &mailPB.MsgBroadcastNtf{}, s.MsgBroadcast)

	// 新手引导进度更新
	c.HandlerCMD(commonPB.MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP, &hallPB.UpdateGuideProgressRsp{}, s.UpdateGuideProgressRsp)
}
