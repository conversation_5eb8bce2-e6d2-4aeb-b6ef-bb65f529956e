package role_hall

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	mockHall "chaossrv/mocktables/hall"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func HallPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	// // 进入钓点请求
	// // time.AfterFunc(config.TimeCommWait, func() {
	// // 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_ENTER_FISHERY_REQ), mockHall.EnterFisheryReq())
	// })

	// // 查询房间信息
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ), mockHall.GetRoomInfoReq())
	// })

	// // 查询道具信息
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_ITEM_INFO_REQ), mockHall.GetItemInfoReq())
	// })

	// // // 根据类型查询道具信息
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_REQ), mockHall.GetItemInfoByType())
	// })

	// // 查询商品购买信息
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_GOODS_BUY_INFO_REQ), mockHall.GetGoodsBuyInfoReq())
	// })

	// // 商品购买
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_STORE_BUY_REQ), mockHall.StoreBuyReq())
	// })

	// // 上次游戏信息
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_LAST_GAME_INFO_REQ), mockHall.GetLastGameInfoReq())
	// })

	// // 玩家信息请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_PLAYER_INFO_REQ), mockHall.GetPlayerInfoReq())
	// })

	// // // 使用道具请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_USE_ITEM_REQ), mockHall.UseItemReq())
	// })

	// // 首次进入大厅请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ), mockHall.FirstEnterHallReq())
	// })

	// 更新竿架信息请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ), mockHall.UpdateRodRigInfoReq())
	// })

	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_PLAYER_NAME_REQ), mockHall.ModifyPlayerName(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_PLAYER_AVATAR_REQ), mockHall.ModifyPlayerAvatarReq(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_STAT_LIST_REQ), mockHall.GetStatsListReq(rb.UID))
	// })

	// 查询干架信息请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_USE_ITEM_REQ), mockHall.UseItemReq())
	// })

	// // 首次进入大厅请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ), mockHall.FirstEnterHallReq())
	// })

	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_REQ), mockHall.GetRodRigInfoReq())
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_PLAYER_NAME_REQ), mockHall.ModifyPlayerName(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_PLAYER_AVATAR_REQ), mockHall.ModifyPlayerAvatarReq(rb.UID))
	// })

	// 删除干架信息请求
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_DELETE_ROD_RIG_INFO_REQ), mockHall.DeleteRodRigInfoReq())
	// })

	// 旅行背包测试
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_TRIP_BAG_REQ), mockHall.GetTripBag1(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_TRIP_BAG_REQ), mockHall.GetTripBag2(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_TRIP_BAG_REQ), mockHall.ModifyTripBagReq1(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MODIFY_TRIP_BAG_REQ), mockHall.ModifyTripBagReq2(rb.UID))
	// })

	// 杆包测试
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ), mockHall.LoadTripRodReq(rb.UID))
	// rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ), mockHall.LoadTripRodReq2(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ), mockHall.GetTripRodReq(rb.UID))
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ), mockHall.GetTripRodReq(rb.UID))
	// 	// rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_DEL_TRIP_ROD_REQ), mockHall.DelTripRodReq(rb.UID))
	// })

	// 实名认证
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_REAL_NAME_AUTH_REQ), mockHall.RealNameAuth(rb.UID))
	// })

	// 商品购买
	// time.AfterFunc(config.TimeCommWait, func() {
	// 	rb.Report.EnterTripTimeS = time.Now().UnixMilli()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_STORE_BUY_REQ), mockHall.StoreBuyReq())
	// })

	// 新手引导进度更新		
	time.AfterFunc(config.TimeCommWait, func() {
		rb.Report.EnterTripTimeS = time.Now().UnixMilli()
		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ), mockHall.UpdateGuideProgressReq())
	})
}

// func LoopEnterPond(rb *robot.Robot) bool {
// 	if rb == nil {
// 		return false
// 	}

// 	rb.ExecuteType = config.ExecuteTypeStress
// 	rb.HallData = &hallData.HallData{}
// 	return SendNextHallReq(rb, int32(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ), false)
// }

// // SendNextHallReq 压测钓鱼大厅相关处理
// func SendNextHallReq(rb *robot.Robot, msgId int32, exit bool) bool {
// 	if rb == nil {
// 		logrus.Errorf("load hall failed, robot is nil")
// 		return false
// 	}

// 	if rb.ExecuteType != config.ExecuteTypeStress {
// 		return false
// 	}

// 	if exit {
// 		logrus.Debugf("robot:%d, exit hall", rb.UID)
// 		return true
// 	}

// 	var msg proto.Message

// 	msg = nil

// 	switch msgId {
// 	case int32(commonPB.MsgID_CMD_FIRST_ENTER_HALL_REQ):
// 		msg = mockHall.FirstEnterHallReq()
// 	case int32(commonPB.MsgID_CMD_GET_ROD_RIG_INFO_REQ):
// 		msg = mockHall.GetRodRigInfoReq()
// 	case int32(commonPB.MsgID_CMD_GET_ITEM_INFO_BY_TYPE_REQ):
// 		msg = mockHall.GetItemInfoByType()
// 	case int32(commonPB.MsgID_CMD_GET_TRIP_ROD_REQ):
// 		msg = mockHall.GetTripRodReq(rb.UID)
// 	case int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ):
// 		var rigId int32 = 1 
// 		if rb.HallData != nil && len(rb.HallData.RodRigInfo) > 0 {
// 			rigId = rb.HallData.RodRigInfo[0].GetRigId()
// 		}
// 		msg = mockHall.LoadTripRodReq(rigId, 1)
// 	case int32(commonPB.MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ):
// 		msg = mockHall.UpdateRodRigInfoReq()
// 	case int32(commonPB.MsgID_CMD_GET_ROOM_INFO_REQ):
// 		msg = mockHall.GetRoomInfoReq()
// 	// 触发加道具
// 	case int32(commonPB.MsgID_CMD_STORE_BUY_REQ):
// 		msgId, msg = GetRbAddItemInfo(rb)
// 	case int32(commonPB.MsgID_CMD_ENTER_FISHERY_REQ):
// 		msg = mockHall.EnterFisheryReq()
// 	default:
// 		logrus.Errorf("SendNextHallReq: robot:%d unknown msgId %d", rb.UID, msgId)
// 	}

// 	logrus.Debugf("SendNextHallReq: robot:%d, msgId:%s", rb.UID, commonPB.MsgID_name[msgId])

// 	if msg != nil {
// 		rb.Client.SendPbMsg(int(msgId), msg)
// 	}

// 	return false
// }

// func GetRbAddItemInfo(rb *robot.Robot) (int32, proto.Message) {
// 	if rb == nil || rb.HallData == nil {
// 		return 0, nil
// 	}

// 	itemList := make([]*commonPB.ItemBase, 0)
// 	var msgId int32
// 	var msgBody proto.Message


// 	switch rb.HallData.AddItemReason {
// 	case config.BuyReasonBagRig:
// 		itemList = GetBagRigItemList(rb)
// 		msgId = int32(commonPB.MsgID_CMD_LOAD_TRIP_ROD_REQ)
// 		msgBody = mockHall.LoadTripRodReq(1, 1)
// 	case config.BuyReasonPondCost:
// 		itemList = GetEnterPondCostItem(rb)
// 		msgId = int32(commonPB.MsgID_CMD_ENTER_FISHERY_REQ)
// 		msgBody = mockHall.EnterFisheryReq()
// 	default:
// 	}

// 	AddItem(rb, itemList)

// 	return msgId, msgBody
// }

// func GetEnterPondCostItem(rb * robot.Robot) []*commonPB.ItemBase {
// 	itemList := []*commonPB.ItemBase {
// 		{
// 			ItemId:    101,
// 			ItemCount: 1000,
// 		},
// 		{
// 			ItemId:    102,
// 			ItemCount: 1000,
// 		},
// 	}

// 	return itemList
// }

// func GetBagRigItemList(rb *robot.Robot) []*commonPB.ItemBase {
// 	if rb == nil || rb.HallData == nil || rb.HallData.BagRigInfo == nil {
// 		return nil
// 	}

// 	itemList := make([]*commonPB.ItemBase, 0)
// 	for _, itemInfo := range rb.HallData.BagRigInfo.GetInfo() {
// 		if itemInfo.GetInstanceId() == "" {
// 			itemList = append(itemList, &commonPB.ItemBase{ItemId: itemInfo.GetItemId(), ItemCount: 1})
// 		}
// 	}

// 	return itemList
// }

// func AddItem(rb *robot.Robot, itemList []*commonPB.ItemBase) {
// 	if rb == nil || len(itemList) <= 0 {
// 		return
// 	}

// 	ctx := interceptor.NewRpcClientCtx(
// 		interceptor.WithProductId(1),
// 		interceptor.WithPlayerId(rb.UID),
// 		interceptor.WithClientID(1001))

// 	item_kit.SendReward(ctx, rb.UID, itemList, commonPB.ITEM_SOURCE_TYPE_IST_GM, true)
// }
