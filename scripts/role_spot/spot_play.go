package role_spot

import (
	"chaossrv/internal/robot"
)

func SpotPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	// // 使用WaitGroup来等待所有请求完成
	// var wg sync.WaitGroup
	// rb.SpotData = &spotData.SpotData{}

	// // 钓点服情景请求
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	if rb.HallData == nil {
	// 		logrus.Errorf("robot:%d spotData is nil", rb.UID)
	// 	} else {
	// 		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_SPOT_SCENE_REQ), mockSpot.GetSpotSceneReq(rb.HallData))
	// 		time.Sleep(config.TimeCommWait)
	// 	}
	// })

	// // 抛竿请求
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_THROW_ROD_REQ), mockSpot.ThrowRodReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 中鱼请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FISH_HOOK_REQ), mockSpot.FishHookReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 收竿请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_CATCH_ROD_REQ), mockSpot.CatchRodReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 鱼入护请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	req := mockSpot.FishEntryOptReq(rb.SpotData.HookFishInstance)
	// 	if req != nil {
	// 		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FISH_ENTRY_OPT_REQ), req)
	// 		time.Sleep(config.TimeCommWait)
	// 	}
	// })

	// // 鱼护中鱼信息请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_KEEPNET_FISH_INFO_REQ), mockSpot.KeepnetFishInfoReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 鱼护操作请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	req := mockSpot.FishKeepnetOptReq(rb.SpotData.Keepnet)
	// 	if req != nil {
	// 		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FISH_KEEPNET_OPT_REQ), req)
	// 		time.Sleep(config.TimeCommWait)
	// 	}
	// })

	// // 房间玩家详情
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_REQ), mockSpot.GetRoomPlayerInfoReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 选择钓点
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_CHOOSE_SPOT_REQ), mockSpot.ChooseSpotReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 搏鱼请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_FISH_BATTLE_FISH_REQ), mockSpot.FishBattleReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 离开房间请求
	// wg.Wait()
	// wg.Add(1)
	// safego.Go(func() {
	// 	defer wg.Done()

	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_EXIT_ROOM_REQ), mockSpot.ExitRoomReq())
	// 	time.Sleep(config.TimeCommWait)
	// })

	// // 等待所有请求完成
	// wg.Wait()
	
	// 压测钓鱼
	//LoopHookFish(rb)
}
