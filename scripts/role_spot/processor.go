package role_spot

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"
	"time"

	"chaossrv/scripts/role_loop"

	spotData "chaossrv/internal/robot/spot_data"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"github.com/sirupsen/logrus"
)

// SpotRoleScript
type SpotRoleScript struct {
	TargetRobot *robot.Robot
}

// GetSpotSceneRsp 获取鱼场信息返回
func (s *SpotRoleScript) GetSpotSceneRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.GetSpotSceneRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, get spot scene rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.Ret.GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		// 获取鱼场信息失败
		logrus.Errorf("robot:%d, get spot scene rsp:%s", s.TargetRobot.UID, rsp.String())
		return fmt.Errorf("get spot scene rsp error")
	}

	// 抛竿请求
	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_THROW_ROD_REQ))

	return nil
}

// ThrowRod 抛竿返回
func (s *SpotRoleScript) ThrowRodRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.ThrowRodRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, throw rod rsp:%v", s.TargetRobot.UID, rsp.String())
		return fmt.Errorf("rsp is nil")
	}

	// 判断是否加道具
	if rsp.GetRet().GetCode() == commonPB.ErrCode_ERR_SPOT_NOT_ENOUGH_POWER {
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_STORE_BUY_REQ))
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_USE_ITEM_REQ))
		time.Sleep(1 * time.Second)
	
		return nil
	}

	if rsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, throw rod rsp:%s", s.TargetRobot.UID, rsp.String())
		// 重新抛竿
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_THROW_ROD_REQ))
		return nil
	}

	// 重置搏鱼次数
	s.TargetRobot.SpotData.BattleTimes = 0

	// 重置中鱼检测次数
	s.TargetRobot.SpotData.HookTimes = 0


	// 开始中鱼请求
	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_SPOT_HOOK_START_REQ))
	// // 下次中鱼请求间隔时间
	// s.TargetRobot.SpotData.HookInterval = rsp.SyncControl.GetIntervalTime()
	// // 中鱼请求
	// role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_HOOK_REQ))

	logrus.Debugf("robot:%d, throw rod pondId:%d, rigId:%d, syncInfo:%v", s.TargetRobot.UID, rsp.GetPondId(), rsp.GetRigId(), rsp.GetSyncControl().String())

	return nil
}

// FishHook 中鱼返回
func (s *SpotRoleScript) FishHookRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.FishHookRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, fish hook rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.GetFishInfo().GetFishId() > 0 {
		logrus.Debugf("robot:%d, fish hook pondId:%d, rigId:%d, fishInfo:%+v", s.TargetRobot.UID, rsp.GetPondId(), rsp.GetRigId(), rsp.GetFishInfo())
		
		if s.TargetRobot.SpotData.BattleTimes <= 0 {
			s.TargetRobot.SpotData.BattleTimes += 1
			// 搏鱼请求
			time.AfterFunc(1*time.Second, func() {
				role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_BATTLE_FISH_REQ))
			})
		} else {
			return nil
		}
	} else {
		// 中鱼请求
		time.AfterFunc(time.Duration((s.TargetRobot.SpotData.HookInterval + 1)) * time.Millisecond, func() {
			role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_HOOK_REQ))
		})
		// s.TargetRobot.SpotData.HookTimes++
		// if s.TargetRobot.SpotData.HookTimes >= 10 {
		// 	// 收竿
		// 	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_CATCH_ROD_REQ))
		// } else {
		// 	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_HOOK_REQ))
		// }
	}

	logrus.Debugf("robot:%d, fish hook pondId:%d, rigId:%d, fishInfo:%+v", s.TargetRobot.UID, rsp.GetPondId(), rsp.GetRigId(), rsp.GetFishInfo())

	return nil
}

// CatchRodRsp 收竿返回
func (s *SpotRoleScript) CatchRodRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.CatchRodRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, catch rod rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.GetFishInfo() != nil && rsp.GetFishInfo().GetInstanceId() != "" {
		if s.TargetRobot.SpotData == nil {
			s.TargetRobot.SpotData = &spotData.SpotData{}
		}
		s.TargetRobot.SpotData.HookFishInstance = rsp.GetFishInfo().GetInstanceId()
		// 入护请求
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_ENTRY_OPT_REQ))
	} else {
		// 抛竿请求
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_THROW_ROD_REQ))
	}

	logrus.Debugf("robot:%d, catch rod pondId:%d, rigId:%d, status:%d, fishInfo:%v", s.TargetRobot.UID, rsp.GetPondId(), rsp.GetRigId(), rsp.GetFishResult(), rsp.GetFishInfo())
 
	return nil
}

// FishEntryOptRsp 入护返回
func (s *SpotRoleScript) FishEntryOptRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.FishEntryOptRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, fish entry rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	// if rsp.GetRet().GetCode() == commonPB.ErrCode_ERR_SPOT_KEEPENT_FULL {
	// 	// 离开房间
	// 	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_EXIT_ROOM_REQ))
	// } else {
	// 	// 抛竿请求
	// 	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_THROW_ROD_REQ))
	// }

	// 抛竿请求
	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_THROW_ROD_REQ))

	logrus.Debugf("robot:%d, entry fish instance:%s, action:%d, fishWeight:%d", s.TargetRobot.UID, rsp.GetFishInstance(), rsp.GetAction(), rsp.GetFishWeight())

	return nil
}

// FishKeepnetOptRsp 鱼护操作返回
func (s *SpotRoleScript) FishKeepnetOptRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.FishKeepnetOptRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, fish keepnet opt rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, fish keepnet opt instance:%s, action:%d, fishWeight:%d", s.TargetRobot.UID, rsp.GetFishInstance(), rsp.GetAction(), rsp.GetFishWeight())

	return nil
}

// KeepnetFishInfoRsp 鱼护鱼详情返回
func (s *SpotRoleScript) KeepnetFishInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.KeepnetFishInfoRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, keepnet fish info rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if s.TargetRobot.SpotData == nil {
		s.TargetRobot.SpotData = &spotData.SpotData{}
	}

	// 保存鱼护中鱼的信息
	s.TargetRobot.SpotData.Keepnet = make([]string, 0, len(rsp.GetFishInfo()))
	for _, fishInfo := range rsp.GetFishInfo() {
		s.TargetRobot.SpotData.Keepnet = append(s.TargetRobot.SpotData.Keepnet, fishInfo.GetInstanceId())
	}

	logrus.Debugf("robot:%d, keepnet fish info fishInfo:%v", s.TargetRobot.UID, rsp.GetFishInfo())

	return nil
}

// GetRoomAllPlayerInfoRsp 房间玩家信息
func (s *SpotRoleScript) GetRoomAllPlayerInfoRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.GetRoomAllPlayerInfoRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, room player info rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d,room player info playerList:%v", s.TargetRobot.UID, rsp.GetPlayerInfo())

	return nil
}

// ChooseSpotRsp 进入房间返回
func (s *SpotRoleScript) ChooseSpotRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.ChooseSpotRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, choose spot rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, choose spot rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, choose spot rsp:%v", s.TargetRobot.UID, rsp)

	return nil
}

// FishBattleRsp 搏鱼返回
func (s *SpotRoleScript) FishBattleRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.FishBattleRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, battle fish rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, battle fish rsp:%v", s.TargetRobot.UID, rsp)
		//return fmt.Errorf("rsp is nil")
	}
	// 收杆
	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_CATCH_ROD_REQ))

	logrus.Debugf("robot:%d, battle fish rsp:%v", s.TargetRobot.UID, rsp)

	return nil
}

// ExitRoomRsp 离开房间返回
func (s *SpotRoleScript) ExitRoomRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.ExitRoomRsp)

	if rsp == nil {   
		logrus.Errorf("robot:%d, room player info rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, room player info rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	return nil
}

// PlayerEnergyChangeNtf 体力变化通知
func (s *SpotRoleScript) PlayerEnergyChangeNtf(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.PlayerEenrgyChangeNtf)
	logrus.Debugf("uid:%d, PlayerEnergyChangeNtf:%v", s.TargetRobot.UID, rsp)
	return nil
}

// HookStartRsp 开始中鱼返回
func (s *SpotRoleScript) HookStartRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*spotPB.HookStartRsp)
	logrus.Debugf("uid:%d, HookStartRsp:%v", s.TargetRobot.UID, rsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, hook start rsp:%v", s.TargetRobot.UID, rsp.String())
		return fmt.Errorf("rsp is nil")
	}


	if rsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, throw rod rsp:%s", s.TargetRobot.UID, rsp.String())
		// 重新请求中鱼
		role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_SPOT_HOOK_START_REQ))
		return nil
	}

	// 下次中鱼请求间隔时间
	s.TargetRobot.SpotData.HookInterval = rsp.SyncControl.GetIntervalTime()
	// 中鱼请求
	role_loop.LoopHookReq(s.TargetRobot, int32(commonPB.MsgID_CMD_FISH_HOOK_REQ))

	return nil
}
