package role_spot

import (
	"chaossrv/internal/robot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
)

// InitSpot 初始化钓点服返回
func InitSpot(rb *robot.Robot) {
	// 世界相关
	c := rb.Client
	s := &SpotRoleScript{
		rb,
	}

	// 进入钓点情景
	c.HandlerCMD(commonPB.MsgID_CMD_GET_SPOT_SCENE_RSP, &spotPB.GetSpotSceneRsp{}, s.GetSpotSceneRsp)

	// 抛竿返回
	c.HandlerCMD(commonPB.MsgID_CMD_THROW_ROD_RSP, &spotPB.ThrowRodRsp{}, s.ThrowRodRsp)

	// 中鱼返回
	c.HandlerCMD(commonPB.MsgID_CMD_FISH_HOOK_RSP, &spotPB.FishHookRsp{}, s.FishHookRsp)

	// 收竿返回
	c.HandlerCMD(commonPB.MsgID_CMD_CATCH_ROD_RSP, &spotPB.CatchRodRsp{}, s.CatchRodRsp)

	// 入护操作
	c.HandlerCMD(commonPB.MsgID_CMD_FISH_ENTRY_OPT_RSP, &spotPB.FishEntryOptRsp{}, s.FishEntryOptRsp)

	// 鱼护操作
	c.HandlerCMD(commonPB.MsgID_CMD_FISH_KEEPNET_OPT_RSP, &spotPB.FishKeepnetOptRsp{}, s.FishKeepnetOptRsp)

	// 鱼护鱼详情
	c.HandlerCMD(commonPB.MsgID_CMD_KEEPNET_FISH_INFO_RSP, &spotPB.KeepnetFishInfoRsp{}, s.KeepnetFishInfoRsp)

	// 房间玩家信息
	// c.HandlerCMD(commonPB.MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_RSP, &spotPB.GetRoomAllPlayerInfoRsp{}, s.GetRoomAllPlayerInfoRsp)

	// 选择钓点
	c.HandlerCMD(commonPB.MsgID_CMD_CHOOSE_SPOT_RSP, &spotPB.ChooseSpotRsp{}, s.ChooseSpotRsp)

	// 搏鱼请求
	c.HandlerCMD(commonPB.MsgID_CMD_FISH_BATTLE_FISH_RSP, &spotPB.FishBattleRsp{}, s.FishBattleRsp)

	// 离开房间返回
	c.HandlerCMD(commonPB.MsgID_CMD_EXIT_ROOM_RSP, &spotPB.ExitRoomRsp{}, s.ExitRoomRsp)

	// 体力变化通知
	c.HandlerCMD(commonPB.MsgID_CMD_ENERGY_CHANGE_NTF, &spotPB.PlayerEenrgyChangeNtf{}, s.PlayerEnergyChangeNtf)

	// 开始中鱼请求
	c.HandlerCMD(commonPB.MsgID_CMD_SPOT_HOOK_START_RSP, &spotPB.HookStartRsp{}, s.HookStartRsp)
}
