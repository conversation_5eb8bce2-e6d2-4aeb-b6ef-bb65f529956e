package role_task

import (
	"chaossrv/internal/robot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
)

func InitTask(rb *robot.Robot) {
	c := rb.Client
	s := &TaskRoleScript{
		rb,
	}

	c.HandlerCMD(commonPB.MsgID_CMD_TASK_GET_LIST_RSP, &taskPB.GetTaskListRsp{}, s.GetTaskList)

	c.HandlerCMD(commonPB.MsgID_CMD_TASK_UPDATE_NTF, &taskPB.UpdateTaskNTF{}, s.UpdateTask)

	c.HandlerCMD(commonPB.MsgID_CMD_TASK_REWARD_RSP, &taskPB.RewardTaskRsp{}, s.RewardTask)

	c.HandlerCMD(commonPB.MsgID_CMD_TASK_PROGRESS_RSP, &taskPB.TaskProgressRsp{}, s.GetTaskProgressRsp)

	c.<PERSON><PERSON>CM<PERSON>(commonPB.MsgID_CMD_TASK_REWARD_PROGRESS_RSP, &taskPB.TaskProgressRewardRsp{}, s.RewardTaskProgressRsp)

}
