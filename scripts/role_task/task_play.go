package role_task

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	"time"

	mockTask "chaossrv/mocktables/task"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TaskPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	// 成就测试
	time.AfterFunc(config.TimeCommWait, func() {
		rb.Report.EnterTripTimeS = time.Now().UnixMilli()
		// 获取列表
		// rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_GET_LIST_REQ), mockTask.GetTaskList(commonPB.TASK_CATEGORY_TC_ACHIEVE))

		// // 查看进度
		// rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_PROGRESS_REQ), mockTask.GetTaskProgress(commonPB.TASK_CATEGORY_TC_ACHIEVE))

		// // 领奖
		// rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_REWARD_REQ), mockTask.RewardTask(commonPB.TASK_CATEGORY_TC_ACHIEVE, 20132014))

		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_REWARD_PROGRESS_REQ), mockTask.RewardProgressTask(commonPB.TASK_CATEGORY_TC_ACHIEVE, 0))
	})

}
