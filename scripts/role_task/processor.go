package role_task

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"github.com/sirupsen/logrus"
)

type TaskRoleScript struct {
	TargetRobot *robot.Robot
}

func (s *TaskRoleScript) GetTaskList(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.GetTaskListRsp)
	if rsp.TaskList == nil || len(rsp.TaskList) == 0 {
		logrus.Debugf("robot:%d taskList is nil", s.TargetRobot.UID)
		logrus.Debugf("taskList data: %+v", rsp)
		return fmt.Errorf("taskList is nil")
	}

	logrus.Infof("GetTaskList, robot:%d, taskList:%+v", s.TargetRobot.UID, rsp.TaskList)

	return nil
}

func (s *TaskRoleScript) UpdateTask(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.UpdateTaskNTF)
	if rsp.Info == nil || len(rsp.Info) == 0 {
		logrus.Debugf("robot:%d taskInfo is nil", s.TargetRobot.UID)
		return fmt.Errorf("taskInfo is nil")
	}
	if len(rsp.Info) > 0 {
		info := rsp.Info[0]
		s.TargetRobot.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_REWARD_REQ), &taskPB.RewardTaskReq{TaskId: info.TaskId})
		logrus.Debugf("robot:%d, send reward:%v", s.TargetRobot.UID, info.TaskId)
	}

	logrus.Infof("UpdateTask, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.Info)
	return nil
}

func (s *TaskRoleScript) RewardTask(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.RewardTaskRsp)
	logrus.Debugf("RewardTask rsp:%+v", rsp)
	if rsp.Info == nil {
		logrus.Debugf("robot:%d taskInfo is nil", s.TargetRobot.UID)
		return fmt.Errorf("taskInfo is nil")
	}
	logrus.Infof("rewadtTask, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.Info)
	return nil
}

func (s *TaskRoleScript) GetTaskProgressNtf(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.TaskProgressNTF)
	logrus.Debugf("TaskProgressNtf rsp rsp:%+v", rsp)
	if rsp.List == nil {
		logrus.Debugf("robot:%d taskProgress is nil", s.TargetRobot.UID)
		return fmt.Errorf("taskPondList is nil")
	}
	logrus.Infof("TaskProgressNtf, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.List)
	return nil
}

func (s *TaskRoleScript) GetTaskProgressRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.TaskProgressRsp)
	logrus.Debugf("TaskProgressRsp rsp rsp:%+v", rsp)
	if rsp.List == nil {
		logrus.Debugf("robot:%d taskProgress is nil", s.TargetRobot.UID)
		return fmt.Errorf("taskProgress is nil")
	}
	logrus.Infof("TaskProgressRsp, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.List)
	return nil
}

func (s *TaskRoleScript) RewardTaskProgressRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*taskPB.TaskProgressRewardRsp)
	logrus.Debugf("TaskProgressRsp :%+v", rsp)
	return nil
}

// func (s *TaskRoleScript) UpdateTaskReward(iRsp socket.IProto) error {
// 	rsp := iRsp.(*taskPB.TaskPondUpdateNTF)
// 	logrus.Debugf("TaskPondUpdateNtf rsp:%+v", rsp)
// 	if rsp.PondList == nil {
// 		return fmt.Errorf("TaskPondNtf is nil")
// 	}
// 	if len(rsp.PondList) > 0 {
// 		info := rsp.PondList[0]
// 		s.TargetRobot.Client.SendPbMsg(int(commonPB.MsgID_CMD_TASK_REWARD_TASK_POND_REQ), &taskPB.TaskPondProgressRewardReq{
// 			PondId:      info.PondId,
// 			RewardIndex: info.RewardIndex + 1,
// 		})
// 	}
// 	logrus.Infof("rewadtTask, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.PondList)
// 	return nil
// }

// func (s *TaskRoleScript) RewardTask(iRsp socket.IProto) error {
// 	rsp := iRsp.(*taskPB.TaskPondProgressRewardRsp)
// 	logrus.Debugf("reward Rsp rsp:%+v", rsp)
// 	if rsp.PondInfo == nil {
// 		return fmt.Errorf("TaskPondNtf is nil")
// 	}
// 	logrus.Infof("rewadtTask, robot:%d, taskInfo:%v", s.TargetRobot.UID, rsp.PondInfo)
// 	return nil
// }
