package role_login

import (
	automated "chaossrv/internal"
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/sirupsen/logrus"
	"time"
)

type LoginRoleScript struct {
	TargetRobot *robot.Robot
}

func (l *LoginRoleScript) LoginHandler(iRsp socket.IProto) error {
	loginRsp := iRsp.(*loginPB.LoginRsp)

	if loginRsp.Ret.Code == 0 {
		uid := loginRsp.PlayerId
		l.TargetRobot.UID = uid
		l.TargetRobot.Client.BindUid(uid) // 将UID跟actor绑定
		l.TargetRobot.LoginRsp = loginRsp
		logrus.Debugf("Rsp : %s ", protox.ToJson(loginRsp))

		// TODO 进入大厅操作
		// TODO 测试logout
		// l.TargetRobot.Client.SendPbMsg(int(commonPB.MsgID_CMD_LOGOUT_REQ), mocktables.Logout(uid))
		// mocktables.Logout(uid)

		// 登录事件统计
		timeS := l.TargetRobot.Report.LoginTimeS
		loginRtt := time.Now().UnixMilli() - timeS

		if l.TargetRobot.ClientNtfCh != nil {
			ret := automated.StateHandle{}
			ret.Code = automated.ConstCsLoginOk
			ret.DeviceID = l.TargetRobot.DeviceCode
			ret.UID = uid
			ret.TimeStat = loginRtt

			l.TargetRobot.ClientNtfCh <- ret
		}

		l.TargetRobot.ScriptObj.Play(l.TargetRobot)
		l.TargetRobot.State = robot.ROBOT_STATE_RUNNING
	} else {
		logrus.Errorf("login_fail:%s", protox.ToJson(loginRsp))
		if l.TargetRobot.ClientNtfCh != nil {
			ret := automated.StateHandle{}
			ret.Code = automated.ConstCsLoginFail
			ret.DeviceID = l.TargetRobot.DeviceCode
			l.TargetRobot.State = robot.ROBOT_STATE_DISCONNECT

			l.TargetRobot.ClientNtfCh <- ret
		}

	}

	return nil
}

func (l *LoginRoleScript) DeleteAccountHandler(iRsp socket.IProto) error {
	rsp := iRsp.(*loginPB.DeleteAccountRsp)
	logrus.Debugf("rsp : %+v ", rsp)
	return nil
}

func (l *LoginRoleScript) AnotherLoginNtfHandler(iRsp socket.IProto) error {
	anotherLoginNtfRsp := iRsp.(*gatePB.GateAnotherLoginNtf)
	logrus.Debugf("Rsp 顶号[%s] : %s ", l.TargetRobot.DeviceCode, protox.ToJson(anotherLoginNtfRsp))

	if l.TargetRobot.ClientNtfCh != nil {
		ret := automated.StateHandle{}
		ret.Code = automated.ConstCsTickOut
		ret.DeviceID = l.TargetRobot.DeviceCode
		ret.UID = l.TargetRobot.UID
		l.TargetRobot.ClientNtfCh <- ret
	}
	l.TargetRobot.State = robot.ROBOT_STATE_ANOTHER

	return nil
}
