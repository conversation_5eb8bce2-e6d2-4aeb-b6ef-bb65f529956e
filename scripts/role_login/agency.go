package role_login

import (
	"chaossrv/internal/robot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
)

func InitLogin(rb *robot.Robot) {
	// 登录态
	c := rb.Client
	s := &LoginRoleScript{
		rb,
	}
	c.HandlerCMD(commonPB.MsgID_CMD_LOGIN_RSP, &loginPB.LoginRsp{}, s.<PERSON><PERSON><PERSON>)
	c.HandlerCMD(commonPB.MsgID_CMD_DELETE_ACCOUNT_RSP, &loginPB.DeleteAccountRsp{}, s.DeleteAccountHandler)
	c.HandlerCMD(commonPB.MsgID_GATE_ANOTHER_LOGIN_NTF, &gatePB.GateAnotherLoginNtf{}, s.AnotherLoginNtfHandler)
}
