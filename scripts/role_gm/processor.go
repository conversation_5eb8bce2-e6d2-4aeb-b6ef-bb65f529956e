package role_gm

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	"github.com/sirupsen/logrus"
)

type GmRoleScript struct {
	TargetRobot *robot.Robot
}

func (s *GmRoleScript) GmOperateRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*gmPB.GmOperateRsp)

	if rsp == nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("robot:%d, Faild rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil or err : %v", rsp)
	}
	logrus.Debugf("robot:%d, [GmoperateRsp]:%s", s.TargetRobot.UID, rsp.String())

	return nil
}
