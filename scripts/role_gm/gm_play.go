package role_gm

import (
	"chaossrv/internal/robot"

	mockGm "chaossrv/mocktables/gm"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)
                    
func GmPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	safego.Go(func() {
		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GM_OPERATE_REQ), mockGm.GetGmOperateItem(rb.UID))
		// for {
		// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GM_OPERATE_REQ), mockGm.GmClearWeather())
		// 	time.Sleep(1 * time.Second)

		// }
	})

}

func GmSendMail(rb *robot.Robot) {
	if rb == nil {
		return
	}

	safego.Go(func() {
		rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_GM_OPERATE_REQ), mockGm.SendMail())
	})

}
