package role_msg

import (
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	mockMsg "chaossrv/mocktables/msg"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func MsgPlay(rb *robot.Robot) {
	if rb == nil {
		return
	}

	// 查询邮件信息
	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MSG_GET_MAIL_LIST_REQ), mockMsg.MailListReq())
	time.Sleep(config.TimeCommWait)

	// 读邮件
	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MSG_READ_MAIL_REQ), mockMsg.ReadMailReq())
	time.Sleep(config.TimeCommWait)

	// 领取邮件
	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_MSG_CLAIM_REWARD_ATTACH_REQ), mockMsg.ClaimAllMailReq())
	time.Sleep(config.TimeCommWait)
}
