package role_msg

import (
	"chaossrv/internal/robot"
	"chaossrv/internal/robot/socket"
	"fmt"

	mailPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/msg"

	"github.com/sirupsen/logrus"
)

type MsgRoleScript struct {
	TargetRobot *robot.Robot
}

func (s *MsgRoleScript) MsgListRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*mailPB.GetMailListRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, MsgListRsp rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, MsgListRsp:%v", s.TargetRobot.UID, rsp)

	return nil
}


func (s *MsgRoleScript) ReadMailRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*mailPB.ReadMailRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, ReadMailRsp rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, ReadMailRsp:%v", s.TargetRobot.UID, rsp)

	return nil
}

func (s *MsgRoleScript) ClaimMailRsp(iRsp socket.IProto) error {
	rsp := iRsp.(*mailPB.ClaimRewardAttachRsp)

	if rsp == nil {
		logrus.Errorf("robot:%d, ClaimRewardAttachRsp rsp:%v", s.TargetRobot.UID, rsp)
		return fmt.Errorf("rsp is nil")
	}

	logrus.Debugf("robot:%d, ClaimRewardAttachRsp:%v", s.TargetRobot.UID, rsp)

	return nil
}