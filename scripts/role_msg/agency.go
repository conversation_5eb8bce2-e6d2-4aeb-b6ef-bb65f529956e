package role_msg

import (
	"chaossrv/internal/robot"

	mailPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/msg"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func InitMsg(rb *robot.Robot) {
	// 消息处理
	c := rb.Client
	s := &MsgRoleScript{
		rb,
	}

	// 邮件列表返回
	c.HandlerCMD(commonPB.MsgID_CMD_MSG_GET_MAIL_LIST_RSP, &mailPB.GetMailListRsp{}, s.MsgListRsp)

	// 读邮件返回
	c.HandlerCMD(commonPB.MsgID_CMD_MSG_READ_MAIL_RSP, &mailPB.ReadMailRsp{}, s.ReadMailRsp)

	// 领奖励
	c.HandlerCMD(commonPB.MsgID_CMD_MSG_CLAIM_REWARD_ATTACH_RSP, &mailPB.ClaimRewardAttachRsp{}, s.ClaimMailRsp)
}
