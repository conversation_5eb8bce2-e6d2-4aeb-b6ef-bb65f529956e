package ws

import (
	"github.com/gorilla/websocket"
	"log"
	"net/http"
	"net/url"
	"testing"
)

func TestWsClient(t *testing.T) {
	// 创建WebSocket URL
	u := url.URL{Scheme: "ws", Host: "************:21201", Path: "/ws"}
	t.Logf("connecting to %s", u.String())

	// 建立连接
	header := make(http.Header)
	header.Set("Sec-WebSocket-Protocol", "network")

	c, _, err := websocket.DefaultDialer.Dial(u.String(), header)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	t.Log("success conn ...", c.Subprotocol())
}
