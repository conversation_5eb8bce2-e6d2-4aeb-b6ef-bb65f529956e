# 登录注册接口压力测试指南

## 项目概述

这是一个基于Go语言开发的游戏服务器压力测试工具，专门用于测试登录注册接口的性能和稳定性。

## 核心功能

- 🤖 **智能机器人模拟**: 支持大量虚拟用户同时登录
- 📊 **实时监控**: Web界面实时显示压测数据
- 🔧 **灵活配置**: 支持多种登录方式和参数调整
- 📈 **性能统计**: 详细的登录RTT、成功率等指标

## 快速开始

### 1. 配置压测参数

编辑 `cmd/config.yml` 文件：

```yaml
# 目标服务器配置
st_web_api: 192.168.1.54:21101      # Web API地址
st_gateway: 192.168.1.54:21201      # 网关地址
st_tcp: 192.168.1.54:31201          # TCP连接地址

# 压测配置
st_player_num: 1000                 # 总机器人数量
multi_player: 50                    # 每秒并发登录数
st_uid_assign_begin: ************   # 设备码起始值

# 连接方式
socket_type: ws                     # ws(WebSocket) 或 tcp
```

### 2. 启动压测服务

```bash
# 方式1: 使用启动脚本
./start_stress_test.sh

# 方式2: 直接运行
./chaos
```

### 3. 监控压测状态

访问以下URL查看实时数据：

- **监控页面**: http://localhost:13009/robot/info
- **JSON API**: http://localhost:13009/robot/api
- **GM管理**: http://localhost:13009/robot/gm

### 4. 控制压测规模

```bash
# 增加100个机器人
curl "http://localhost:13009/robot/gm?ot=add&param=100"

# 减少50个机器人  
curl "http://localhost:13009/robot/gm?ot=minus&param=50"

# 调整日志级别 (0:Panic 1:Fatal 2:Error 3:Warn 4:Info 5:Debug 6:Trace)
curl "http://localhost:13009/robot/gm?ot=log&param=4"
```

## 支持的登录方式

### 1. 访客登录 (默认)
- 自动生成唯一设备码
- 无需账号密码
- 适合快速压测

### 2. 账号密码登录
修改 `mocktables/mock_login.go` 中的 `LoginAccount()` 函数

### 3. Token登录
修改 `mocktables/mock_login.go` 中的 `LoginToken()` 函数

## 关键指标说明

| 指标 | 说明 |
|------|------|
| 目标机器人数 | 配置的总压测用户数 |
| 投放Robot数 | 实际创建的机器人数 |
| Online数 | 成功登录的机器人数 |
| 登录失败数 | 登录失败的机器人数 |
| 登录顶号数 | 被踢下线的机器人数 |
| RTT超过阈值人数 | 登录响应时间超过3秒的机器人数 |

## 压测流程

```mermaid
graph TD
    A[启动压测服务] --> B[读取配置文件]
    B --> C[创建机器人管理器]
    C --> D[按配置速度创建机器人]
    D --> E[机器人建立连接]
    E --> F[发送登录请求]
    F --> G{登录成功?}
    G -->|是| H[执行压测逻辑]
    G -->|否| I[记录失败统计]
    H --> J[30秒后断开连接]
    I --> K[重试或结束]
```

## 自定义压测逻辑

如需修改登录后的行为，编辑 `scripts/IRobotRoleImpl.go` 中的 `LoginStressTest` 函数：

```go
func (s *RoleScript) LoginStressTest(rb *robot.Robot) {
    // 在这里添加登录后的验证逻辑
    // 例如：获取用户信息、发送心跳等
    
    logrus.Debugf("机器人 %d 登录压测完成", rb.UID)
    
    // 可自定义连接保持时间
    time.AfterFunc(30*time.Second, func() {
        if rb.Client != nil && rb.Client.IsConn() {
            rb.Client.Close()
        }
    })
}
```

## 故障排查

### 常见问题

1. **连接失败**
   - 检查目标服务器地址是否正确
   - 确认服务器端口是否开放
   - 检查网络连通性

2. **登录失败率高**
   - 降低 `multi_player` 参数减少并发压力
   - 检查服务器性能和资源使用情况
   - 确认登录接口是否正常工作

3. **内存占用过高**
   - 减少 `st_player_num` 参数
   - 缩短机器人连接保持时间

### 日志分析

```bash
# 查看详细日志
tail -f ../../logs/chaos/chaos_13009.log

# 过滤登录相关日志
grep "login" ../../logs/chaos/chaos_13009.log
```

## 性能建议

- **小规模测试**: 100-500个机器人，并发10-20/秒
- **中等规模测试**: 500-1000个机器人，并发30-50/秒  
- **大规模测试**: 1000+个机器人，根据服务器性能调整并发数

## 技术架构

- **语言**: Go 1.23.3
- **通信协议**: WebSocket/TCP + Protobuf
- **并发模型**: Goroutine池
- **状态管理**: FSM有限状态机
- **监控**: HTTP接口 + 实时统计

---

💡 **提示**: 首次使用建议先进行小规模测试，确认配置正确后再逐步增加压测规模。
