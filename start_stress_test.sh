#!/bin/bash

echo "======================================"
echo "     登录注册接口压力测试启动脚本"
echo "======================================"

# 检查配置文件
if [ ! -f "cmd/config.yml" ]; then
    echo "错误: 配置文件 cmd/config.yml 不存在"
    exit 1
fi

echo "1. 检查配置..."
echo "   - 目标服务器: $(grep st_gateway cmd/config.yml | cut -d: -f2 | tr -d ' ')"
echo "   - 压测机器人数量: $(grep st_player_num cmd/config.yml | cut -d: -f2 | tr -d ' ')"
echo "   - 并发登录速度: $(grep multi_player cmd/config.yml | cut -d: -f2 | tr -d ' ')个/秒"

echo ""
echo "2. 启动压测服务..."
echo "   访问监控页面: http://localhost:13009/robot/info"
echo "   API接口: http://localhost:13009/robot/api"
echo ""

# 启动服务
./chaos

echo "压测服务已停止"
