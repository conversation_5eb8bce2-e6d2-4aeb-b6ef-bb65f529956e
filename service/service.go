package service

import (
	automated "chaossrv/internal"
	playerMgr "chaossrv/internal/manager"
	"chaossrv/internal/oam"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

type StressService struct {
}

func (s *StressService) Init() error {
	logrus.Infof("测试网关 : %s 测试人数:%d",
		viper.GetString("st_gateway"),
		viper.GetInt("st_player_num"))

	return nil
}

func (s *StressService) Start() error {
	oam.InitOamRoute()

	playerMgr.MainLoop()
	return nil
}

func (s *StressService) Stop() error {
	logrus.Infof("stress service stopped.")
	return nil
}

func (s *StressService) ForceStop() error {
	logrus.Infof("stress service force stopped.")
	return nil
}

var RootCmd = &cobra.Command{
	Use:                "Chaos",
	Short:              "压力测试命令，没有子命令时默认启动压力测试服，提供web接口压测",
	Long:               `压力测试命令，没有子命令时默认启动压力测试服，提供web接口压测`,
	DisableFlagParsing: true,
	Run: func(cmd *cobra.Command, args []string) {
		automated.Run(&StressService{})
	},
}
