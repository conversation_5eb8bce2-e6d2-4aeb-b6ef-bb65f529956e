// Package statistics 统计数据
package statistics

import (
	automated "chaossrv/internal"
	playerMgr "chaossrv/internal/manager"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"

	"github.com/spf13/viper"
)

type UsageStat struct {
	Code            int           `json:"code"`            // 压测环境路径
	Path            string        `json:"path"`            // 压测环境路径
	SwapTotal       uint64        `json:"swapTotal"`       // 总内存
	SwapFree        uint64        `json:"swapFree"`        // 使用内存
	DiskTotal       float64       `json:"diskTotal"`       // 总磁盘
	DiskUsed        float64       `json:"diskUsed"`        // 使用磁盘
	DiskUsedPercent float64       `json:"diskUsedPercent"` // 使用率
	SlotIntervalCmd string        `json:"slotIntervalCmd"` // 老虎机执行间隔
	LogLevelCmd     string        `json:"logLevelCmd"`     // 日志级别
	ExecDuration    time.Duration `json:"execDuration"`    // 执行总时长
	TimeStamp       time.Time     `json:"timeStamp"`       // 时间戳
	TargetRobotNum  int           `json:"targetRobotNum"`  // 目标机器人数量
	PutRobotNum     int           `json:"putRobotNum"`     // 投放机器人数量
	OnlineRobotNum  int           `json:"onlineRobotNum"`  // 在线机器人数量
	RttThreshold    int64         `json:"rttThreshold"`    // 当前RTT时间
	LoginDelayNum   int           `json:"loginDelayNum"`   // loginDelayNum
	AverageDt       float32       `json:"averageDt"`       // 当前平均处理DT时间

	StAppUpdateFail  int `json:"stAppUpdateFail"`  // StAppUpdateFail
	StPreLoginFail   int `json:"stPreLoginFail"`   // StPreLoginFail
	StLoginFailedNum int `json:"stLoginFailedNum"` // StLoginFailedNum
	StRecvFailedNum  int `json:"stRecvFailedNum"`  // StRecvFailedNum
	StSendFailedNum  int `json:"stSendFailedNum"`  // StSendFailedNum
	StSocketFail     int `json:"stSocketFail"`     // StSocketFail
	StLoginTickOut   int `json:"stLoginTickOut"`   // 顶号数量
}

const (
	B  = 1
	KB = 1024 * B
	MB = 1024 * KB
	GB = 1024 * MB
)

var ProcRunTime time.Time

func init() {
	ProcRunTime = timex.Now()
}

func GetLocalIp() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "localhost"
	}
	for _, address := range addrs {
		// 检查ip地址判断是否回环地址
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return "localhost"
}

// Header header 打印表头信息
func Header(w http.ResponseWriter) {
	// fmt.Fprintf(w, "\n")
	// 打印的时长都为毫秒 总请数
	printLineTop(w)
	localIp := GetLocalIp()
	fmt.Fprintf(w, " 【压测程序统计】 %s\n", viper.GetString("st_gateway"))
	vPort := viper.GetString("http_port")
	fmt.Fprintf(w, " GM 信息 : http://%s:%s/robot/info\n", localIp, vPort)
	fmt.Fprintf(w, " ShutDown : http://%s:%s/shutdown/deadline={second}\n", localIp, vPort)
	fmt.Fprintf(w, " GM增加操作 : http://%s:%s/robot/gm?ot=add&param={num}\n", localIp, vPort)
	fmt.Fprintf(w, " GM减少操作 : http://%s:%s/robot/gm?ot=minus&param={num}\n", localIp, vPort)
	// fmt.Fprintf(w, " Slot 间隔 : http://localhost:%s/gm?ot={tick}&param={1:工作时间 0：n秒}\n", vPort)
	fmt.Fprintf(w, " 日志 : http://%s:%s/gm?ot={log}&param={0:Panic 1:Fatal 2:Error "+
		"3:Warn 4:Info 5:Debug 6:Trace }\n", localIp, vPort)
	fmt.Fprintf(w, " 执行时长 ：%v\n", automated.GetProcRunTime())
	fmt.Fprintf(w, " 并发登录：%s\n", fmt.Sprintf("%d/s", viper.GetInt32("multi_player")))
	fmt.Fprintf(w, " TimeStamp :%s\n", time.Now())
	printLineRow(w)
	return
}

func printLineTop(w http.ResponseWriter) {
	fmt.Fprintf(w, "─────┬───────┬───────┬───────┬────────┬────────┬────────┬────────┬────────┬────────┬────────\n")
}

func printLineRow(w http.ResponseWriter) {
	fmt.Fprintf(w, "─────┼───────┼───────┼───────┼────────┼────────┼────────┼────────┼────────┼────────┼────────\n")
}

func printLineInfo(w http.ResponseWriter) {
	fmt.Fprintf(w, "-----------------------------------\n")
}

func ShowInfo(w http.ResponseWriter, r *http.Request) {
	Header(w)

	nTargetNum := playerMgr.GetTargetNum()
	nPlayerNum := playerMgr.GetPlayNum()
	nPlayerOnlineNum := playerMgr.GetOnlineNum()
	nLoginRttNum := playerMgr.GetRttNum()

	fmt.Fprintf(w, "目标机器人数：%d\n", nTargetNum)
	// printLineInfo(w)
	// fmt.Fprintf(w, "\n")
	fmt.Fprintf(w, "投放Robot数：%d\n", nPlayerNum)

	fmt.Fprintf(w, "Online数：%d\n", nPlayerOnlineNum)

	fmt.Fprintf(w, "登录失败数：%d\n", playerMgr.StLoginFailedNum)

	fmt.Fprintf(w, "登录顶号数：%d\n", playerMgr.StLoginTick)

	// fmt.Fprintf(w, "\n")
	fmt.Fprintf(w, "RTT超过阈值人数：%d\n", nLoginRttNum)

	printLineInfo(w)

	// if playerMgr.GetSlotStatus() {
	// 	fmt.Fprintf(w, "Slot状态 ：开启 | 执行间隔 : %s\n", config.GetSlotTickRule())
	// } else {
	// 	fmt.Fprintf(w, "Slot状态 ：关闭 | 执行间隔 : %s\n", config.GetSlotTickRule())
	// }
}

func InfoApi(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("content-type", "application/json")

	if r.Method == http.MethodOptions {
		return
	}

	nTargetNum := playerMgr.GetTargetNum()
	nPlayerNum := playerMgr.GetPlayNum()
	nPlayerOnlineNum := playerMgr.GetOnlineNum()
	nLoginRttNum := playerMgr.GetRttNum()

	var diskTotal, diskUsed, diskUsedPercent float64

	d, _ := disk.Usage("/")
	diskTotal = float64(d.Total / GB)
	diskUsed = float64(d.Used / GB)

	mem, _ := mem.VirtualMemory()

	useData := &UsageStat{
		Code:            http.StatusOK,
		Path:            viper.GetString("st_gateway"),
		SwapTotal:       mem.SwapTotal,
		SwapFree:        mem.SwapTotal - mem.SwapFree,
		DiskTotal:       diskTotal,
		DiskUsed:        diskUsed,
		DiskUsedPercent: diskUsedPercent,
		SlotIntervalCmd: viper.GetString("st_spin_tick"),
		LogLevelCmd:     viper.GetString("log_level"),
		ExecDuration:    time.Since(ProcRunTime),
		TimeStamp:       time.Now(),
		TargetRobotNum:  nTargetNum,
		PutRobotNum:     nPlayerNum,
		OnlineRobotNum:  nPlayerOnlineNum,
		RttThreshold:    int64(nLoginRttNum),
		AverageDt:       float32(nLoginRttNum),

		StAppUpdateFail:  playerMgr.StAppUpdateFail,
		StPreLoginFail:   playerMgr.StPreLoginFail,
		StLoginFailedNum: playerMgr.StLoginFailedNum,
		StRecvFailedNum:  playerMgr.StRecvFailedNum,
		StSendFailedNum:  playerMgr.StSendFailedNum,
		StSocketFail:     playerMgr.StSocketFail,
		LoginDelayNum:    playerMgr.GetRttNum(),
		StLoginTickOut:   playerMgr.StLoginTick,
	}

	json.NewEncoder(w).Encode(useData)
	// fmt.Fprintf(w, "success %v ", useData.TimeStamp)
}
