package oam

import (
	playerMgr "chaossrv/internal/manager"
	"chaossrv/internal/statistics"
	"fmt"
	"net/http"
	"strconv"

	"github.com/sirupsen/logrus"
)

func InitOamRoute() {
	http.HandleFunc("/robot/info", statistics.ShowInfo)
	http.HandleFunc("/robot/api", statistics.InfoApi)
	http.HandleFunc("/robot/gm", gmHandler)
}

func gmHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("content-type", "application/json")

	if r.Method == http.MethodOptions {
		return
	}

	opType := r.FormValue(OType) // 操作类型
	opParam := r.FormValue(OParam)

	logrus.Debugf("gmHandler %s, %s", opType, opParam)

	iParam, err := strconv.Atoi(opParam)
	if err != nil {
		fmt.Fprintf(w, "参数错误\n")
		return
	}

	switch opType {
	case PAdd:
		fmt.Fprintf(w, "添加%d人，目标%d人，Online%d \n", iParam, playerMgr.GetTargetNum(), playerMgr.GetOnlineNum())
		if iParam <= 0 {
			fmt.Fprintf(w, "投放机器人（%d）小于等于0 \n", iParam)
			return
		}
		playerMgr.IncrTargetNum(iParam)
		logrus.Infof("收到请求添加机器人 %d", iParam)
		playerMgr.Payout()
	case PMinus:
		fmt.Fprintf(w, "减少%d人，目标%d人，Online%d \n", iParam, playerMgr.GetTargetNum(), playerMgr.GetOnlineNum())

		playNum := playerMgr.GetPlayNum()
		if playNum < iParam && playNum > 0 {
			fmt.Fprintf(w, "投放机器人（%d）小于等于目标 \n", playNum)
			return
		}
		playerMgr.IncrTargetNum(-iParam)
		playerMgr.Shutdown()
		// TODO 回收机器人
	case PLogLevel:
		fmt.Fprintf(w, "变更日志: %s \n", logrus.Level(iParam).String())
		logrus.SetLevel(logrus.Level(iParam))
	}
}
