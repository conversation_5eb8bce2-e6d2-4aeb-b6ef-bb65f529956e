package timer

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
)

type Robot struct {
	// 你已有的字段
	TimerManager *TimerManager
	// 其他字段
}

func NewRobot() *Robot {
	return &Robot{
		TimerManager: NewTimerManager(),
	}
}

func (r *Robot) Start() {
	// 使用int64类型的ID启动定时任务示例
	r.TimerManager.StartPeriodicTask(3*time.Second, 0, func() {
		// 发送心跳包逻辑
		logrus.Infof("发送心跳包")
	})

	r.TimerManager.StartOneShotTask(3*time.Second, func() {
		// 5秒后执行的初始化任务
		logrus.Infof("5s task")
	})
	r.TimerManager.Start()
}

func (r *Robot) Stop() {
	// 停止所有定时任务
	r.TimerManager.Stop()
	// 其他清理逻辑
}

func TestTimer(t *testing.T) {
	robot := NewRobot()
	robot.Start()
	logrus.Infof("start")
	time.Sleep(30 * time.Second)
	robot.Stop()
}
