// timer.go
package timer

import (
	"context"
	"sync"
	"time"
)

// TimerTask 定时任务结构体
type TimerTask struct {
	Id       int32
	Interval time.Duration
	Handler  func()
	FlushTs  time.Time // 刷新时间
	Count    int32     // 执行次数
	Target   int32     // 目标次数
}

// TimerManager 定时器管理器
type TimerManager struct {
	ctx     context.Context
	tasks   map[int32]*TimerTask // 任务列表
	ticker  *time.Ticker         // 定时器
	cancel  context.CancelFunc   // 取消函数
	mutex   sync.RWMutex         // 互斥锁
	counter int32                // id计数器
}

// NewTimerManager 创建定时器管理器
func NewTimerManager() *TimerManager {
	return &TimerManager{
		tasks:   make(map[int32]*TimerTask),
		mutex:   sync.RWMutex{},
		counter: 1, // 初始化计数器
	}
}

func (tm *TimerManager) Start() {
	tm.ctx, tm.cancel = context.WithCancel(context.Background())
	go func() {
		for {
			select {
			case <-tm.ctx.Done():
				return
			case <-tm.ticker.C:
				tm.mutex.Lock()
				// 执行触发
				tm.doFunc()
				tm.mutex.Unlock()
			}
		}
	}()
}

// Stop 停止所有定时任务
func (tm *TimerManager) Stop() {
	tm.cancel()
	// 清理数据
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	tm.tasks = make(map[int32]*TimerTask)
}

func (tm *TimerManager) doFunc() {
	now := time.Now()
	finished := make([]int32, 0)
	for _, task := range tm.tasks {
		// 超过目标值
		if task.Target > 0 && task.Count >= task.Target {
			finished = append(finished, task.Id)
			continue
		}
		// 触发执行
		if now.After(task.FlushTs) {
			task.Count++
			task.Handler()
			if task.Target == 0 || task.Count < task.Target {
				task.FlushTs = task.FlushTs.Add(task.Interval)
			} else {
				finished = append(finished, task.Id)
			}
		}
	}
	tm.removeTaskLocked(finished...)
	tm.refreshTimer()
}

// AddTimerTask 添加定时任务
func (tm *TimerManager) AddTimerTask(tasks ...*TimerTask) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	for _, task := range tasks {
		if task.Id == 0 {
			task.Id = tm.counter
			tm.counter++
		}
		// 如果任务已存在，先停止它
		if _, exists := tm.tasks[task.Id]; exists {
			tm.removeTaskLocked(task.Id)
		}

		tm.tasks[task.Id] = task
	}
	tm.refreshTimer()
}

// RemoveTimerTask 移除定时任务
func (tm *TimerManager) RemoveTimerTask(taskIds ...int32) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	tm.removeTaskLocked(taskIds...)
	tm.refreshTimer()
}

// removeTaskLocked 移除任务（需要在持有锁的情况下调用）
func (tm *TimerManager) removeTaskLocked(taskIds ...int32) {
	// 移除任务表数据
	for _, id := range taskIds {
		delete(tm.tasks, id)
	}
}

func (tm *TimerManager) stopTimer() {
	if tm.ticker != nil {
		tm.ticker.Stop()
		tm.ticker = nil
	}
}

func (tm *TimerManager) refreshTimer() {
	tm.stopTimer()
	if len(tm.tasks) == 0 {
		return
	}

	// 计算最近一个任务的执行时间
	var near time.Time
	for _, task := range tm.tasks {
		if near.IsZero() {
			near = task.FlushTs
		}
		if task.FlushTs.Before(near) {
			near = task.FlushTs
		}
	}
	if near.IsZero() {
		return
	}

	tm.ticker = time.NewTicker(time.Until(near))
}

// GetTask 获取任务信息
func (tm *TimerManager) GetTask(taskID int32) (*TimerTask, bool) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	task, exists := tm.tasks[taskID]
	return task, exists
}

// ListTasks 列出所有任务ID
func (tm *TimerManager) ListTasks() []int32 {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	ids := make([]int32, 0, len(tm.tasks))
	for id := range tm.tasks {
		ids = append(ids, id)
	}
	return ids
}

// StartOneShotTask 启动一次性任务
func (tm *TimerManager) StartOneShotTask(delay time.Duration, handler func()) *TimerTask {
	task := &TimerTask{
		Interval: delay,
		Handler:  handler,
		Target:   1,
		FlushTs:  time.Now().Add(delay),
	}
	tm.AddTimerTask(task)
	return task
}

// StartPeriodicTask 启动周期性任务
func (tm *TimerManager) StartPeriodicTask(interval time.Duration, targetCount int32, handler func()) *TimerTask {
	task := &TimerTask{
		Interval: interval,
		Handler:  handler,
		Target:   targetCount,
		FlushTs:  time.Now().Add(interval),
	}
	tm.AddTimerTask(task)
	return task
}

func (tm *TimerManager) Reset() {
	tm.refreshTimer()
}