package config

import "time"

const (
	TickFrame       = "*/5 * * * * ?"          // MainLoop帧函数
	TickSlotWorkJob = "*/5 * 9-22 * * MON-FRI" // 工作日期，早上9点到下午22点 每5秒一次
	TickSlot        = "*/5 * * * * ?"          // 不限制，每N秒
)

const (
	TimeStaticLog     = 5               // 在线人数检测间隔秒数
	TimeLoginDelay    = 2 * time.Second // Socket连接后，多久进行Login协议发送
	TimeCommWait      = 2 * time.Second // 通用等待时间
	TimeRttLoginDelay = 3000            // 毫秒 登录延时统计阀值
)

// 脚本执行类型
const (
	ExecuteTypeNone   = iota // 默认(测试接口)
	ExecuteTypeStress = 1    // 压测功能
)

// 购买道具原因
const (
	BuyReasonNone     = iota
	BuyReasonBagRig   = iota + 1 // 购买背包竿组道具
	BuyReasonPondCost = iota + 2 // 钓场消耗
)
