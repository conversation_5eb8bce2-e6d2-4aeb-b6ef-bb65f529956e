package automated

// StateHandle State 玩家通信状态
type StateHandle struct {
	Code     int
	UID      uint64
	DeviceID string
	Actor    interface{}
	TimeStat int64
}

const (
	ConstCsOk          = iota // 执行成功  value --> 0
	ConstCsFail               // 执行失败
	ConstCsAppInfoOk          // 登录前获取App信息成功
	ConstCsAppInfoFail        // 登录前获取App信息失败
	ConstCsSocketOk           // Socket连接成功
	ConstCsSocketFail         // Socket失败
	ConstCsSessFail           // 获取Session失败
	ConstCsLoginOk            // 登录成功
	ConstCsLoginFail          // 登录失败
	ConstCsTickOut            // 玩家被踢下线
	ConstCsLogOut             // 玩家下线
)
