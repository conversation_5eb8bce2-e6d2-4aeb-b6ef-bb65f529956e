package automated

import (
	"context"
	"flag"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/sirupsen/logrus"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
	"math/rand"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"strconv"
	"sync"
	"syscall"
	"time"
)

// Service 是每个使用服务必须实现的接口
type Service interface {
	// Init 作一些初始化操作。 参数 e 为服务提供了一些通用的接口。
	// 如果该函数返回非 nil 错误， driver 不会执行 Start 函数
	Init() error

	// Start 启动服务，服务可以在该函数内部 block
	// driver 在调用 Init 函数返回成功后，调用 Start 函数启动服务
	Start() error

	// Stop 停止服务 服务可以在该函数内部 block
	Stop() error

	// ForceStop 强制停止，不执行退休策略
	ForceStop() error
}

func setup() {
	// 命令行输入参数
	flag.String("config", "", "config file (default is ./config.yml)")
	flag.String("log_level", "error", "log_level")
	flag.String("st_gateway", "", "st_gateway port")
	flag.Int("st_player_num", 0, "st_player_num")

	pflag.CommandLine.AddGoFlagSet(flag.CommandLine)
	pflag.Parse()
	viper.BindPFlags(pflag.CommandLine)

	if cfgFile := viper.GetString("config"); cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.SetConfigFile("./cmd/config.yml")
	}

	viper.AutomaticEnv()
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("读取配置文件失败:%v", err))
	}

	// http.Handle("/metrics", promhttp.Handler())
}

var ProcRunTime time.Time

// Run 启动服务
func Run(service Service) {
	rand.Seed(time.Now().UnixNano())

	ProcRunTime = time.Now()

	setup()

	logLevel := viper.GetString(dict.ConfigLogLevel)     // 日志级别
	printFormatJson := viper.GetBool(dict.ConfigLogJson) // 输出格式化Json
	logdog.SetupLog(logLevel, printFormatJson)

	if viper.GetBool(dict.ConfigLogWrite) {
		fileName := viper.GetString(dict.ConfigRpcServerName) + "_" + viper.GetString(dict.ConfigRpcPort)
		logDir := viper.GetString(dict.ConfigLogDir) // 日志文件目录
		logdog.SetWriteFile(fileName, logDir, logLevel)
	}

	if err := service.Init(); err != nil {
		panic(err)
	}
	logrus.Infof("[%s]服务初始化完成", viper.GetString("rpc_server_name"))
	wg := sync.WaitGroup{}

	wg.Add(1)
	go func() {
		defer wg.Done()
		defer logdog.RecoverAndLog("run-svc")
		if err := service.Start(); err != nil {
			panic(err)
		}
	}()

	viper.SetDefault("http_port", "80")
	var httpSvr = &http.Server{
		Addr:    "0.0.0.0:" + viper.GetString("http_port"),
		Handler: nil, // 使用默认的 mux
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		logrus.Infof("启动 http 服务，addr: %s", httpSvr.Addr)
		if err := httpSvr.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			panic(err)
		}
	}()

	// 配置文件watch
	go func() {
		interval := time.NewTicker(5 * time.Minute)
		defer interval.Stop()

		for {
			select {
			case <-interval.C:
				if err := viper.ReadInConfig(); err != nil {
					panic(fmt.Sprintf("读取配置文件失败:%v", err))
				}

				if lv, err := logrus.ParseLevel(viper.GetString("log_level")); err == nil {
					logrus.Tracef("reset log lever %s", viper.GetString("log_level"))
					logrus.SetLevel(lv)
				} else {
					// fmt.Println("parse level failed:", viper.GetString("log_level"), err)
					logrus.Errorf("reset log lever %s error", viper.GetString("log_level"))
				}
			}
		}
	}()

	signalChan := make(chan os.Signal, 1)
	runningChan := make(chan bool, 1)

	runningChan = EleganceRestart(service, runningChan)

	signal.Notify(signalChan, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	go func() {
		sig := <-signalChan
		logrus.Infof("接收到结束信号%v，服务将退出", sig)
		runningChan <- true
	}()
	<-runningChan
	logrus.Infoln("服务开始关闭...")

	// time.Sleep(time.Millisecond * 500)

	if err := service.Stop(); err != nil {
		logrus.Errorf("stop service failed[%v]", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := httpSvr.Shutdown(ctx); err != nil {
		logrus.Errorf("停止 http 服务失败")
	} else {
		logrus.Infof("关闭 http 服务成功")
	}
	logdog.StopLog()
	logrus.Infoln("服务关闭完成")
	os.Exit(0)
	wg.Wait()
}

// GetProcRunTime 获取进程运行时间
func GetProcRunTime() time.Duration {
	return time.Since(ProcRunTime)
}

func EleganceRestart(service Service, runningChan chan bool) chan bool {
	var deadLine int
	http.HandleFunc("/shutdown/", func(rw http.ResponseWriter, r *http.Request) {
		logrus.Infof("接收到http关闭服务命令(%s)，准备关闭服务...", r.URL)
		const DeadLine = "deadline"
		value := r.FormValue(DeadLine)
		if len(value) > 0 {
			interval, err := strconv.Atoi(value)
			if err != nil {
				rw.WriteHeader(404)
				rw.Write([]byte("强制关闭时间必须是一个数字"))
				logrus.Errorln("错误的时间格式")
				return
			}
			deadLine = interval
		}
		rw.WriteHeader(200)
		sForce := ""
		if deadLine > 0 {
			sForce = fmt.Sprintf("如果%d秒后服务未关闭，将启动强制解散", deadLine)
			logrus.Infoln(sForce)
			go func() {
				forceStopTick := time.NewTicker(time.Duration(deadLine) * time.Second)
				select {
				case <-forceStopTick.C:
					if err := service.ForceStop(); err != nil {
						logrus.Errorf("force stop service failed[%v]", err)
					}
				}
			}()

		}
		tip := fmt.Sprintf("正在关闭服务，等待服务退休...\n%s", sForce)
		logrus.Infoln(tip)
		rw.Write([]byte(tip))

		runningChan <- true
	})
	return runningChan
}
