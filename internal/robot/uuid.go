package robot

import (
	"strconv"
	"time"

	"github.com/spf13/viper"
)

// GenUUID 获得设备码
func GenDeviceCode(nPlayerNum int64) string {
	var uuid string
	uidBeginBum := viper.GetInt64("st_uid_assign_begin")
	// 初始用0
	if uidBeginBum >= 0 {
		uuid = strconv.FormatInt(uidBeginBum+nPlayerNum, 10)
	} else {
		microTime := time.Now().UnixNano() / 1000000
		uuid = strconv.FormatInt(microTime, 10)
	}

	return uuid
}
