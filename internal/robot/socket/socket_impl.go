package socket

import (
	automated "chaossrv/internal"

	"github.com/golang/protobuf/proto"
	"github.com/spf13/viper"
)

type ISocket interface {
	BindUid(uid uint64)
	Close()
	SendPbMsg(msgId int, msg proto.Message)
	HandlerCMD(iCmd interface{}, rsp interface{}, handle clientHandler) error
	IsConn() bool
}

func NewSocket(playerDeviceCode string, ClientNtfCh chan automated.StateHandle) ISocket {
	socketType := viper.GetString("socket_type")
	switch socketType {
	case "tcp":
		return NewTcpClient(playerDeviceCode, ClientNtfCh)
	case "ws":
		return NewWsClient(playerDeviceCode, ClientNtfCh)
	}
	return nil
}

