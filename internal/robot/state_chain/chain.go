package state_chain

import (
	"context"
	"fmt"
)

type State<PERSON>hain struct {
}

type State int32

type Event int32

type ChainMgr struct {
	current  State
	StateMap map[State]map[Event]StateFunc
}

type EventFc map[Event]StateFunc


type StateFunc func(ctx context.Context, event Event) (nextState State)

func (m *ChainMgr) RegisterStateFunc(state State, event Event, f StateFunc) {

}

// New 创建管理器
func New() *ChainMgr {
	return &ChainMgr{
		// StateMap: make(map[State]StateChain),
	}
}

func (m *ChainMgr) Current() State {
	return m.current
}

func (m *ChainMgr) Execute(ctx context.Context, e Event) error {
	current := m.Current()

	mp, ok := m.StateMap[current]
	if !ok {
		return fmt.Errorf("state %d not found", current)
	}
	f, ok := mp[e]
	if !ok {
		return fmt.Errorf("event %d not found", e)
	}

	next := f(ctx, e)
	m.current = next
	return nil
}



