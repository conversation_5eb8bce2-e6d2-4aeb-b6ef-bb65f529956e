package state_chain

import (
	"context"
	"fmt"
)

type TestHook struct {
	*ChainMgr
}

const (
	State_Enter_Hall State = 1
	State_Get_Room   State = 2
	State_Add_Money  State = 3
)

const (
	Event_rsp     Event = 1
	Event_Success Event = 2
)

func NewTestHook() *TestHook {
	return nil
}

func (t *TestHook) InitHookHandler() {
	t.RegisterStateFunc(State_Enter_Hall, Event_rsp, t.handleEnterHall)
	t.RegisterStateFunc(State_Add_Money, Event_rsp, t.handleAddMoney)
	// t.RegisterStateFunc(State_Get_Room, Event_Get_Room, t.handleGetRoom)
}

func (h *TestHook) handleEnterHall(ctx context.Context, event Event) State {
	fmt.Println("处理进入大厅事件")
	money := 100
	if money <= 100 {
		return State_Add_Money
	}
	nextState := State_Get_Room
	return nextState
}

func (h *TestHook) handleAddMoney(ctx context.Context, event Event) State {
	fmt.Println("处理加钱事件")
	return State_Get_Room
}

func (h *TestHook) handleGetRoom(state FsmState) FsmState {
	fmt.Println("处理查询房间事件")
	nextState, _ := h.XFsm.ExecuteEvent(state)
	return nextState
}
