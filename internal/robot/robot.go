package robot

import (
	automated "chaossrv/internal"
	hallData "chaossrv/internal/robot/hall_data"
	"chaossrv/internal/robot/socket"
	spotData "chaossrv/internal/robot/spot_data"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"github.com/golang/protobuf/proto"
)

type ReportInfo struct {
	LoginTimeS     int64 // Tcp登录时间
	TcpDoneTimeS   int64 // Tcp处理时长
	EnterTripTimeS int64 // 进入行程时间
}

type Robot struct {
	DeviceCode  string
	UID         uint64 // playerId
	ClientNtfCh chan automated.StateHandle
	Client      socket.ISocket
	SerialNo    int64 // 机器人序号
	ScriptObj   IRobotRole
	Report      ReportInfo
	LoginRsp    *loginPB.LoginRsp
	HallData    *hallData.HallData // 大厅数据
	SpotData    *spotData.SpotData // 钓点数据
	ExecuteType int32              // 执行类型
	Mocks       []*MockMsg
	State       ROBOT_STATE
}

type ROBOT_STATE int32

const (
	ROBOT_STATE_INIT       ROBOT_STATE = iota
	ROBOT_STATE_RUNNING                // 正在运行
	ROBOT_STATE_DISCONNECT             // 机器人掉线
	ROBOT_STATE_SHUTDOWN               // 机器人关机
	ROBOT_STATE_ANOTHER                // 机器人关机

)

func NewRobot(no int64) *Robot {
	rb := &Robot{
		SerialNo:   no,
		DeviceCode: GenDeviceCode(no),
	}

	return rb
}

type MockMsg struct {
	Step  int
	MsgId int
	Data  proto.Message
}

// 清空
func (r *Robot) EmptyMock() {
	r.Mocks = make([]*MockMsg, 0)
}

func (r *Robot) PushMock(msgId commonPB.MsgID, data proto.Message) {
	if r.Mocks == nil {
		r.Mocks = make([]*MockMsg, 0)
	}
	r.Mocks = append(r.Mocks, &MockMsg{
		MsgId: int(msgId),
		Data:  data,
	})
}

func (r *Robot) PopMock() *MockMsg {
	if len(r.Mocks) > 0 {
		msg := r.Mocks[0]
		r.Mocks = r.Mocks[1:]
		return msg
	}
	return nil
}

func (r *Robot) IsConnect() bool {
	if r == nil || r.Client == nil {
		return false
	}
	return r.Client.IsConn()
}
