package robot

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/spf13/viper"
)

const (
	RoutePoll    = "poll"    // 轮询
	RouteLB      = "lb"      // 负载均衡
	RouteSpecify = "specify" // 指定  如果设置为这个键值，则默认取第一个地址
)

func GetRouteAddress() string {
	routerStrategy := viper.GetString("rpc_router")
	routerLists := viper.GetStringSlice("route_list")
	retRoute := ""
	switch routerStrategy {
	case RoutePoll:
		nIndex := random.IntN(len(routerLists))
		retRoute = routerLists[nIndex]
	case RouteLB:

	case RouteSpecify:
		retRoute = routerLists[0]
	}
	return retRoute
}
