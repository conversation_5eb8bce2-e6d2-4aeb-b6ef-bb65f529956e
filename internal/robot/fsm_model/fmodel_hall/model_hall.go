package fmodel_hall

import (
	fsmBase "chaossrv/internal/robot/fsm_base"
	"context"
)

type ModelFisher struct {
	*fsmBase.XFsm
}

func Init() *ModelFisher {

}

func (m *ModelFisher) Init() {
	m.XFsm.Init()

}

// 注册处理器
func (m *ModelFisher) ReighterHandler() {

}

func (m *ModelFisher) OnEnterHall(ctx context.Context, event fsmBase.Event) (fsmBase.FsmState, error) {

	return fsmBase.FsmState_Enter_Hall, nil
}
