package fsmBase

type FsmState int32

// 为每个常量显式指定类型 FsmState
const (
	FsmState_None       FsmState = 0
	FsmState_Init       FsmState = 1     // 初始化
	FsmState_Login      FsmState = 2     // 登录
	FsmState_Enter_Hall FsmState = 3     // 进入大厅
	FsmState_Get_Room   FsmState = 4     // 获取房间信息
	FsmState_In_Room    FsmState = 5     // 在房间里
	FsmState_Not_Room   FsmState = 6     // 不在房间
	FsmState_Enter_Pond FsmState = 7     // 进入钓场
	FsmState_Spot_Scene FsmState = 8     // 获取场景信息
	FsmState_Throw_Rod  FsmState = 9     // 抛竿
	FsmState_Logout     FsmState = 10000 // 登出
)

// 自定义 状态事件转换成字符串map
var FsmState2Str = map[FsmState]string{
	FsmState_None:   "None",
	FsmState_Init:   "Init",
	FsmState_Login:  "Login",
	FsmState_Logout: "Logout",
}

// 自定义 状态事件转换成字符串
func (e FsmState) String() string {
	if str, ok := FsmState2Str[e]; ok {
		return str
	}
	return "unknown"
}