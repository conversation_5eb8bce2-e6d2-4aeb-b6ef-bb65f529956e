package fsmBase

// // HookFsm 继承自 XFsm 的子类
// type HookFsm struct {
// 	*XFsm // 嵌入基类
// }

// // NewHookFsm 创建新的 HookFsm 实例
// func NewHookFsm() *HookFsm {
// 	hook := &HookFsm{
// 		XFsm: &XFsm{},
// 	}

// 	// 初始化基类
// 	hook.XFsm.Init()
// 	hook.InitHookHandler()
// 	hook.InitNextState()

// 	return hook
// }

// // InitHookHandler 注册状态机函数
// func (h *HookFsm) InitHookHandler() {
// 	h.XFsm.RegisterStateHandler(FsmState_Enter_Hall, h.handleEnterHall)
// 	h.XFsm.RegisterStateHandler(FsmState_Get_Room, h.handleGetRoom)
// 	h.XFsm.RegisterStateHandler(FsmState_In_Room, h.handleInRoom)
// 	h.XFsm.RegisterStateHandler(FsmState_Not_Room, h.handleNotRoom)
// 	h.XFsm.RegisterStateHandler(FsmState_Enter_Pond, h.handleEnterPond)
// 	h.XFsm.RegisterStateHandler(FsmState_Spot_Scene, h.handleSpotScene)

// }

// // InitNextState 注册下个状态机
// func (h *HookFsm) InitNextState() {
// 	h.XFsm.RegisterNextState(FsmState_Enter_Hall, FsmState_Get_Room)
// 	h.XFsm.RegisterNextState(FsmState_In_Room, FsmState_Spot_Scene)
// 	h.XFsm.RegisterNextState(FsmState_Not_Room, FsmState_Enter_Pond)
// 	h.XFsm.RegisterNextState(FsmState_Enter_Pond, FsmState_Spot_Scene)
// 	h.XFsm.RegisterNextState(FsmState_Spot_Scene, FsmState_Throw_Rod)
// }

// // ExecuteHookEvent 执行 HookFsm 事件
// func (h *HookFsm) ExecuteEvent(state FsmState) (FsmState, error) {
// 	return h.XFsm.ExecuteEvent(state)
// }

// func (h *HookFsm) handleEnterHall(state FsmState) FsmState {
// 	fmt.Println("处理进入大厅事件")
// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }

// func (h *HookFsm) handleGetRoom(state FsmState) FsmState {
// 	fmt.Println("处理查询房间事件")
// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }

// func (h *HookFsm) handleInRoom(state FsmState) FsmState {
// 	fmt.Println("处理进入房间事件")

// 	// TODO 根据当前房间发送请求钓点情景

// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }

// func (h *HookFsm) handleNotRoom(state FsmState) FsmState {
// 	fmt.Println("处理进入房间事件")

// 	// TODO 请求进入房间

// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }

// func (h *HookFsm) handleEnterPond(state FsmState) FsmState {
// 	fmt.Println("处理进入钓场事件")

// 	// TODO 发送进入钓场请求

// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }

// func (h *HookFsm) handleSpotScene(state FsmState) FsmState {
// 	fmt.Println("处理钓点情景事件")

// 	// TODO 发送钓点情景请求

// 	nextState, _ := h.XFsm.ExecuteEvent(state)
// 	return nextState
// }
