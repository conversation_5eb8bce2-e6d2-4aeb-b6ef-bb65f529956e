package fsmBase

import (
	"chaossrv/internal/timer"
	"context"
	"errors"
	"fmt"
)

type Event struct {
	Event FsmEvent
	Args  []interface{}
}

// FmsFunc 定义事件处理函数类型
type FmsFunc func(ctx context.Context, event Event) (nextState FsmState, err error)

// XFsm FSM基类结构
type XFsm struct {
	// eventId到处理函数的映射
	eventFuncs map[FsmState]FmsFunc
	// eventId到下一个eventId的映射
	nextEvents map[FsmState]FsmState
	// 当前状态
	currentState FsmState
	// 状态计时器
	timeMgr *timer.TimerManager
}

func (f *XFsm) Init() {
	f.eventFuncs = make(map[FsmState]FmsFunc)
	f.nextEvents = make(map[FsmState]FsmState)
}

// RegisterStateHandler 注册状态机函数
func (f *XFsm) RegisterStateHandler(state FsmState, handler FmsFunc) {
	if _, ok := f.eventFuncs[state]; ok {
		panic(fmt.Sprintf("状态机 %s 已存在", state))
	}
	f.eventFuncs[state] = handler
}

// RegisterNextState 注册状态机迭代图 (暂时不实现这种模式，配置使用量太大了, 全部协议配置下来会很累)
func (f *XFsm) RegisterNextState(fromState FsmState, toState FsmState, byEvent FsmEvent) {
	// state, ok := FsmState2Str[curState]
	// if !ok {
	// 	panic(fmt.Sprintf("状态机 %s 不存在", state))
	// }
	// f.nextEvents[curState] = nextState
}

// ExecuteEvent 执行指定事件
func (f *XFsm) ExecuteEvent(event Event) error {
	// 检查状态是否存在
	handler, exists := f.eventFuncs[f.currentState]
	if !exists {
		return errors.New("no such state")
	}

	// TODO: 根据事件类型处理

	// 执行事件处理函数
	nextState, err := handler(context.Background(), event)
	if err != nil {
		return err
	}

	if nextState != f.currentState {
		// 切换状态后，把定时器重置
		f.timeMgr.Reset()
	}
	f.currentState = nextState
	return nil
}

// GetCurrentState 获取当前状态
func (f *XFsm) GetCurrentState() FsmState {
	return f.currentState
}

// SetCurrentState 设置当前状态
func (f *XFsm) SetCurrentState(state FsmState) {
	f.currentState = state
}
