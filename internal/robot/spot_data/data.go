package spotData

// SpotData钓点服保存信息
type SpotData struct {
	PondId            int64   `json:"pond_id"`            // 钓场id
	RoomId            string  `json:"room_id"`            // 房间id
	SpotId            int32   `json:"spot_id"`            // 钓点id
	HookFishInstance string   `json:"hook_fish_instance"` // 捕鱼实例
	Keepnet          []string `json:"keepnet"`            // 捕鱼记录(鱼实例列表)
	HookTimes         int32   `json:"hook_times"`         // 单次抛竿中鱼检测次数(抛竿成功后重置)
	HookInterval      int64   `json:"hook_interval"`      // 下一次中鱼请求间隔时间
	BattleTimes       int32   `json:"battle_times"`       // 搏鱼次数
}
