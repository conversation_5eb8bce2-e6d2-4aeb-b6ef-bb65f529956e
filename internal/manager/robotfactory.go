package playermgr

import (
	automated "chaossrv/internal"
	"chaossrv/internal/robot"
	"chaossrv/scripts"
	"sync"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type PlayerMgr struct {
	sync.RWMutex
	PlayerMap map[string]*robot.Robot
}

var PlayMap *PlayerMgr

func init() {
	PlayMap = &PlayerMgr{
		PlayerMap: make(map[string]*robot.Robot),
	}
}

func (mgr *PlayerMgr) Store(key string, val *robot.Robot) {
	mgr.Lock()
	defer mgr.Unlock()
	mgr.PlayerMap[key] = val
}

func (mgr *PlayerMgr) Load(key string) (val *robot.Robot, ok bool) {
	mgr.RLock()
	defer mgr.RUnlock()
	val, ok = mgr.PlayerMap[key]
	return
}

func (mgr *PlayerMgr) Delete(key string) {
	mgr.Lock()
	defer mgr.Unlock()
	mgr.PlayerMap[key] = nil
}

func NewPlayer(deviceCode string, curPlayNum int, clientNtfCh chan automated.StateHandle) *robot.Robot {
	player := robot.NewRobot(int64(curPlayNum))
	player.ClientNtfCh = clientNtfCh

	// 激活脚本
	actor := &scripts.RoleScript{
		TargetRobot: player,
		ClientNtfCh: clientNtfCh,
	}
	if err := actor.Init(player); err != nil {
		logrus.Infof("[%s]机器人创建失败 - [deviceCode]:%s", viper.GetString("rpc_server_name"), deviceCode)
		return nil
	}

	player.ScriptObj = actor

	// errLogin := actor.Login(player)
	// if errLogin != nil {
	// 	return nil
	// }


	logrus.Infof("[%s]角色激活 - [deviceCode]:%s", viper.GetString("rpc_server_name"), deviceCode)

	return player
}
