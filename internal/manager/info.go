package playermgr

import (
	"sync"
	"time"
)

var (
	nPlayerNumMutex       sync.Mutex
	nPlayerOnlineNumMutex sync.Mutex
	nPlayerRunMutex       sync.Mutex
	nPlayerRttNumMutex    sync.Mutex
	nPlayerTargetMutex    sync.Mutex
)

var (
	nTargetNum       = 0 // 目标压测数量(配置，设置的阈值)
	nPlayerNum       = 0 // 实际Add机器人数（真正执行添加的机器人）
	nPlayerOnlineNum = 0 // 登录成功
	nLoginRttNum     = 0 // 登录RTT超过100毫秒用户数
)

var (
	IsSlot = false
)

var ProcRunTime time.Time

func incrPlayNum(incr int) {
	nPlayerNumMutex.Lock()
	nPlayerNum += incr
	nPlayerNumMutex.Unlock()
}

// IncrTargetNum 修改目标数
func IncrTargetNum(incr int) {
	nPlayerTargetMutex.Lock()
	nTargetNum += incr
	nPlayerTargetMutex.Unlock()
}

// IncrOnlinePlayNum 登录统计
func IncrOnlinePlayNum(incr int) {
	nPlayerOnlineNumMutex.Lock()
	nPlayerOnlineNum += incr
	nPlayerOnlineNumMutex.Unlock()
}

// IncrRttPlayNum 登录操作消息RTT延迟数量
func IncrRttPlayNum(incr int) {
	nPlayerRttNumMutex.Lock()
	nLoginRttNum += incr
	nPlayerRttNumMutex.Unlock()
}

// GetPlayNum 获取当前Add的添加机器人数量
func GetPlayNum() int {
	var tmpPlayerNum = 0
	nPlayerNumMutex.Lock()
	tmpPlayerNum = nPlayerNum
	nPlayerNumMutex.Unlock()
	return tmpPlayerNum
}

func GetOnlineNum() int {
	var tmpPlayerNum = 0
	nPlayerOnlineNumMutex.Lock()
	tmpPlayerNum = nPlayerOnlineNum
	nPlayerOnlineNumMutex.Unlock()
	return tmpPlayerNum
}

func GetRttNum() int {
	var tmpPlayerNum = 0
	nPlayerRttNumMutex.Lock()
	tmpPlayerNum = nLoginRttNum
	nPlayerRttNumMutex.Unlock()
	return tmpPlayerNum
}

func GetTargetNum() int {
	var tmpPlayerNum = 0
	nPlayerTargetMutex.Lock()
	tmpPlayerNum = nTargetNum
	nPlayerTargetMutex.Unlock()
	return tmpPlayerNum
}

func GetSlotStatus() bool {
	return IsSlot
}

// tcp info
var (
	StAppUpdateFail  = 0 // AppUpdateFail次数
	StPreLoginFail   = 0 // PreLogin失败
	StLoginFailedNum = 0 // 登录失败次数
	StRecvFailedNum  = 0 // Recv失败次数
	StSendFailedNum  = 0 // Send失败次数
	StSocketFail     = 0 // Socket失败
	StLoginTick      = 0 // 顶号次数
)

var errorMap map[string]int /*创建Error统计集合 */
