package playermgr

import (
	automated "chaossrv/internal"
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/kit/safego"

	"github.com/robfig/cron"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

var wg = sync.WaitGroup{}

// var PlayMap sync.Map

var stateCh = make(chan automated.StateHandle, 100) // 消息状态管道

// addRobot 添加玩家，并且登录
func addRobot(playerNum int) {
	// defer wg.Done()

	deviceCode := robot.GenDeviceCode(int64(playerNum))
	logrus.Infof("[%s] 新增机器人: deviceCode ：%s", viper.GetString("rpc_server_name"), deviceCode)

	player := NewPlayer(deviceCode, GetPlayNum(), stateCh)
	if player == nil {
		logrus.Errorf("【%s】player deviceCode:%s NewPlayer error", viper.GetString("rpc_server_name"), deviceCode)
		return
	}

	PlayMap.Store(deviceCode, player)

	// 总人数增加一个
	incrPlayNum(1)
}

// Payout 机器人派发
func Payout() {

	// 通过Chan处理Client返回信息
	tmpPlayerNum := GetPlayNum()
	tmpTargetNum := GetTargetNum()

	timer := time.NewTicker(1 * time.Second)
	defer timer.Stop()
	multiPlayer := viper.GetInt(config.WordMultiPlayer)

	for {
		select {
		case <-timer.C:
			// 构建当前组工作数
			if tmpPlayerNum >= tmpTargetNum {
				return
			}
			workNum := tmpPlayerNum + multiPlayer
			if workNum > tmpTargetNum {
				workNum = tmpTargetNum
			}
			for i := tmpPlayerNum; i < workNum; i++ {
				// for循环内局部变量赋值
				i2 := i
				safego.Go(func() {
					addRobot(i2) // 如果这里开协程，则会在协程里面阻塞，
				})
			}
			tmpPlayerNum += multiPlayer
		}
	}
}

// Shutdown 机器人关机
func Shutdown() {
	time.Sleep(500 * time.Millisecond)
	tmpPlayerNum := GetPlayNum()
	tmpTargetNum := GetTargetNum()

	// 求解多出来的机器人
	for i := tmpTargetNum; i <= tmpPlayerNum; i++ {
		deviceCode := robot.GenDeviceCode(int64(i))
		player, ok := PlayMap.Load(deviceCode)
		if ok {
			// player.(*robot2.Robot).KillCh <- struct{}{}
			player.State = robot.ROBOT_STATE_SHUTDOWN
		}
	}
}

func lister() {
	for {
		select {
		case retInfo := <-stateCh:
			status := retInfo.Code
			switch status {
			case automated.ConstCsLoginOk:
				IncrOnlinePlayNum(1)

				// 统计下登录RTT超过100毫秒
				if retInfo.TimeStat > config.TimeRttLoginDelay {
					IncrRttPlayNum(1)
				}
			case automated.ConstCsLoginFail:
				incrPlayNum(-1)
				IncrOnlinePlayNum(-1)
				StLoginFailedNum++
			case automated.ConstCsTickOut:
				if retInfo.DeviceID != "" {
					IncrOnlinePlayNum(-1)
					StLoginTick++
				}
			case automated.ConstCsSocketFail:
				logrus.Infof("(%d)Socket连接断开", retInfo.UID)
				// incrPlayNum(-1)
				IncrOnlinePlayNum(-1)
			case automated.ConstCsLogOut:
				if retInfo.DeviceID != "" {
					_, ok := PlayMap.Load(retInfo.DeviceID)
					if ok {
						incrPlayNum(-1)
						IncrOnlinePlayNum(-1)
						PlayMap.Delete(retInfo.DeviceID)
					}
				}
				// StLoginTick++
				logrus.Infof("Robot (%d:%s) logout", retInfo.UID, retInfo.DeviceID)
			}
		}
	}
}

// MainLoop 主循环
func MainLoop() {

	nTargetNum = viper.GetInt("st_player_num") // 缺省值
	bIsOpenWatch := viper.GetBool("watch_log") // 是否开启监控

	lastTime := time.Now()
	safego.Go(func() {
		lister()
	})

	Payout()

	// 帧函数 状态监听器
	c := cron.New()
	c.AddFunc(config.TickFrame, func() {
		tmpPlayerNum := GetPlayNum()
		tmpTargetNum := GetTargetNum()
		nowTime := time.Now()
		spanSec := nowTime.Sub(lastTime).Seconds()

		// 统计
		if spanSec >= config.TimeStaticLog && bIsOpenWatch {
			logrus.Debugf("【检测】目标:%d Robot:%d 在线：%d", tmpTargetNum, tmpPlayerNum, GetOnlineNum())
			lastTime = nowTime
		}

		if tmpPlayerNum == 0 && tmpTargetNum == 0 {
			logrus.Debugf("【当前无机器人在压测】目标:%d Robot:%d 在线：%d", tmpTargetNum, tmpPlayerNum, GetOnlineNum())
		}
	})
	c.Start()
}
