package single_test

import (
	"chaossrv/single_test/tconfig"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// 连接器

// 根据consul 对应服务发起连接

func init() {
	// 读取配置信息
	viper.SetConfigFile("../cmd/config.yml")
	viper.ReadInConfig()
	data := viper.AllSettings()
	// 第二份配置
	viper.SetConfigFile("test.yml")
	viper.ReadInConfig()
	viper.AutomaticEnv()
	err := viper.MergeConfigMap(data)
	if err != nil {
		logrus.Errorf("error:%+v", err)
	}
}

type ConnManager struct {
	connMap sync.Map
}

// 连接管理器
var defaultMgr *ConnManager

func GetDefaultMgr() *ConnManager {
	if defaultMgr == nil {
		defaultMgr = NewSingleton()
	}
	return defaultMgr
}

// 初始化单例
func NewSingleton() *ConnManager {
	// TODO 多grpc实例并发测试,模拟多服务器端并发
	mgr := &ConnManager{
		connMap: sync.Map{},
	}
	return mgr
}

// Dial 指定地址rpc服务端
func (mgr *ConnManager) NewGrpcConnByAddr(addr string) (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	x, ok := mgr.connMap.Load(addr)
	if ok {
		return x.(*grpc.ClientConn), nil
	}

	conn, err := grpc.DialContext(ctx, addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	) // TODO 补充grpc拦截器
	if err != nil {
		return nil, err
	}
	mgr.connMap.Store(addr, conn)
	return conn, err
}

// 基于服务发现获取连接
func (mgr *ConnManager) GetConnByDiscovery(clusterType tconfig.CLUSTER_TYPE, serverName string) (*grpc.ClientConn, error) {
	// TODO: 使用consul 做负载均衡和服务发现

	portMap := viper.GetStringMap("server_port")
	port, ok := portMap[serverName]
	if !ok {
		return nil, fmt.Errorf("server not found")
	}

	clusterAddr := viper.GetString(fmt.Sprintf("cluster.%s", clusterType))

	addr := fmt.Sprintf("%s:%v", clusterAddr, port)

	return mgr.NewGrpcConnByAddr(addr)
}
