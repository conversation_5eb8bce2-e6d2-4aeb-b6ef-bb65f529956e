package single_test

import (
	"chaossrv/single_test/tconfig"
	"chaossrv/single_test/tfuns/asset"
	"testing"
)

func TestItem(t *testing.T) {
	mgr := GetDefaultMgr()
	// conn, err := mgr.NewGrpcConnByAddr(fmt.Sprintf("consul://%s/%s", viper.GetString(dict.ConfigConsulAddr), "asset"))
	conn, err := mgr.GetConnByDiscovery(tconfig.CLUSTER_TYPE_DEV, "asset")
	if err != nil {
		t.Fatalf("err :%+v", err)
	}
	defer conn.Close()
	err = asset.AddItem(conn)
	if err != nil {
		t.Fatalf("err :%+v", err)
	}
}
