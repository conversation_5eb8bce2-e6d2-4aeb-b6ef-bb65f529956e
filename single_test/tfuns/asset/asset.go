package asset

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	assetRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/assetrpc"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

func AddItem(conn *grpc.ClientConn) error {
	ctx := context.Background()
	cli := assetRpc.NewAssetServiceClient(conn)
	rsp, err := cli.OperateItem(ctx, &assetRpc.OperateItemReq{
		ProductId:     1,
		PlayerId:      99999999,
		ItemOperation: commonPB.ITEM_OPERATION_IO_ADD,
		Loots: []*commonPB.OriginLoot{
			&commonPB.OriginLoot{
				Item: &commonPB.Item{
					ItemId:       101,
					ItemCategory: commonPB.ITEM_CATEGORY_IC_CURRENCY,
					ItemType: commonPB.ITEM_TYPE_IT_CURRENCY_COIN,
				},
				Value: 1,
			},
		},
		Source:  0,
		Storage: commonPB.STORAGE_TYPE_ST_STORE,
	})
	if err != nil {
		return err
	}
	logrus.Infof("%s", rsp.String())
	return nil
}
