package multi_work

import (
	"context"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

// 并发任务
func MultiGo(num int, f func()) {
	wg := sync.WaitGroup{}

	for i := 0; i < num; i++ {
		wg.Add(1)
		safego.Go(func() {
			defer wg.Done()
			f()
		})
	}
	wg.Wait()
}

// 真同时并发
func ImmediateMultiGo(num int, f func()) {
	// 准备进程管理
	ctx, cancel := context.WithCancel(context.TODO())
	// 等待管理
	wg := sync.WaitGroup{}
	ready := sync.WaitGroup{}
	ready.Add(num)

	for i := 0; i < num; i++ {
		wg.Add(1)
		// n := i
		go func() {
			defer wg.Done()
			// 进程准备就绪
			ready.Done()
			select {
			case <-ctx.Done():
				f()
			}
		}()
	}
	ready.Wait()
	cancel()
	wg.Wait()
}
