package multi_work

import (
	"fmt"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/spf13/viper"
)

func TestImm(t *testing.T) {
	f := func() {
	}
	ImmediateMultiGo(1000, f)
}

func TestImm2(t *testing.T) {
	viper.Set("consul_addr", "************:8500")
	f := func() {
		conf := cmodel.GetHookConst()
		if conf == nil {
			fmt.Println("conf is nil")
		} else {
			fmt.Println("success!")
		}
	}
	ImmediateMultiGo(1000, f)
}