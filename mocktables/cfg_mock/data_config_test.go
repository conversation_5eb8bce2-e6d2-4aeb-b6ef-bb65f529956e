package cfg_mock

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"testing"
)

func init() {
	viper.SetDefault("consul_addr", "************:8500")
	logrus.SetLevel(logrus.DebugLevel)
}

func TestFishBasicCfg(t *testing.T) {
	t.Log("-> GetAllBasicFishSpecies")
	cmodel.LoadBasicFishSpeciesCfg()
	arr := cmodel.GetAllBasicFishSpecies()
	for k, species := range arr {
		t.Logf("k : %v, v:%v", k, species)
	}

	t.Log("-> GetAllBasicFishQuality")
	cmodel.LoadBasicFishQualityCfg()
	arr2 := cmodel.GetAllBasicFishQuality()
	for k, v := range arr2 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishQuality")
	cmodel.LoadBasicFishCharacterCfg()
	arr3 := cmodel.GetAllBasicFishCharacter()
	for k, v := range arr3 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishHabitus")
	cmodel.LoadBasicFishHabitusCfg()
	arr4 := cmodel.GetAllBasicFishHabitus()
	for k, v := range arr4 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishPeriod")
	cmodel.LoadBasicFishPeriodCfg()
	arr5 := cmodel.GetAllBasicFishPeriod()
	for k, v := range arr5 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishWaterLayer")
	cmodel.LoadBasicFishWaterLayerCfg()
	arr6 := cmodel.GetAllBasicFishWaterLayer()
	for k, v := range arr6 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishTemperature")
	cmodel.LoadBasicFishTemperatureCfg()
	arr7 := cmodel.GetAllBasicFishTemperature()
	for k, v := range arr7 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishTemperature")
	cmodel.LoadBasicFishHabitatWaterCfg()
	arr8 := cmodel.GetAllBasicFishHabitatWater()
	for k, v := range arr8 {
		t.Logf("k : %v, v:%v", k, v)
	}

	t.Log("-> GetAllBasicFishTemperature")
	cmodel.LoadBasicFishBarrierCfg()
	arr9 := cmodel.GetAllBasicFishBarrier()
	for k, v := range arr9 {
		t.Logf("k : %v, v:%v", k, v)
	}
}

func TestFishDistributeCfg(t *testing.T) {
	t.Log("-> GetAllFishDistributePeriod")
	cmodel.LoadFishDistributePeriodCfg()
	arr := cmodel.GetAllFishDistributePeriod()
	for k, v := range arr {
		t.Logf("k : %v, v:%v", k, v)
		for _, pv := range v.Period {
			row := cmodel.GetBasicFishPeriod(pv.PeriodId)
			t.Logf("-> get period row : %v", row)
		}
	}

	t.Log("-> GetAllFishDistribute")
	cmodel.LoadFishDistributeCfg()
	arr2 := cmodel.GetAllFishDistribute()
	for k, v := range arr2 {
		t.Logf("k : %v, v:%v", k, v)
	}
}
