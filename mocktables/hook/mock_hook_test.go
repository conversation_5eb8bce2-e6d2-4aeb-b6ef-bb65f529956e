package mockHook

import (
	"context"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
	"github.com/spf13/viper"
)

func init() {
	viper.SetDefault("consul_addr", "************:8500")
}

func TestThrowRodRpc(t *testing.T) {
	req := &hookRpc.ThrowRodRouteReq{
		PlayerId: 1001,                    // 玩家ID
		PondId:   106000001,               // 池塘ID
		RigId:    1,                       // 钓组ID
		GridInfo: &commonPB.ThrowGridInfo{
			AreaType:     1,               // 区域类型
			ObstacleType: 1,               // 障碍物品类型
			Pos: &commonPB.Position{
				PosX: 10,                  // 坐标x
				PosY: 20,                  // 坐标y
				PosZ: 30,                  // 水深
			},
		},
	}
	rsp, err := ThrowRodRpc(context.Background(), req)
	if err != nil {
		t.Fatalf("Failed to call ThrowRodRpc: %v", err)
	}
	if rsp == nil {
		t.Fatal("Unexpected nil response from ThrowRodRpc")
	}
	t.Logf("%+v", rsp)
}

func TestHookFishRpc(t *testing.T) {

	req := &hookRpc.FishHookRouteReq{
		PlayerId: 1001,                        // 玩家ID
		PondId:  106000001,                    // 池塘ID
		RigId:   1,                            // 钓组ID
		GridInfo: &commonPB.ThrowGridInfo{
			AreaType:     1,                   // 区域类型
			ObstacleType: 1,                   // 障碍物品类型
			Pos: &commonPB.Position{
				PosX: 10,                      // 坐标x
				PosY: 20,					   // 坐标y						
				PosZ: 30,                      // 水深
			},
		},
	}


	rsp, err := HookFishRpc(context.TODO(), req)
	if err != nil {
		t.Fatalf("Failed to call HookFishRpc: %v", err)
	}
	if rsp == nil {
		t.Fatal("Unexpected nil response from HookFishRpc")
	}
	t.Logf("%+v", rsp)
}

func TestCatchRodRpc(t *testing.T) {
	req := &hookRpc.CatchRodRouteReq{
		PlayerId: 1001,             // 玩家ID
		PondId:  106000001,         // 池塘ID
		RigId:   1,                 // 钓组ID
	}
	rsp, err := CatchRodRpc(context.Background(), req)
	if err != nil {
		t.Error(err)
	}
	t.Logf("%+v", rsp)
}