package mockHook

import (
	"context"
	"fmt"

	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
	crpcHook "git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hook"
	"github.com/sirupsen/logrus"
)

// ThrowRodRpc 抛竿请求
func ThrowRodRpc(ctx context.Context, req *hookRpc.ThrowRodRouteReq) (*hookRpc.ThrowRodRouteRsp, error) {
	if req == nil {
		logrus.Errorf("req is nil")
		return nil, nil
	}

	hookRpcCli := crpcHook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, fmt.Errorf("hook rpc client is nil")
	}

	rsp, err := hookRpcCli.GetThrowRodReq(ctx, req)

	if err != nil {
		logrus.Errorf("rpc get err : %v", err)
		return nil, err
	}

	return rsp, nil
}

// HookFishRpc 中鱼请求
func HookFishRpc(ctx context.Context, req *hookRpc.FishHookRouteReq) (*hookRpc.FishHookRouteRsp, error) {
	if req == nil {
		logrus.Errorf("req is nil")
		return nil, nil
	}

	hookRpcCli := crpcHook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, fmt.Errorf("hook rpc client is nil")
	}

	rsp, err := hookRpcCli.GetFishHookReq(ctx, req)

	if err != nil {
		logrus.Errorf("rpc get err : %v", err)
		return nil, err
	}

	return rsp, nil
}

// CatchRodRpc 收杆请求
func CatchRodRpc(ctx context.Context, req *hookRpc.CatchRodRouteReq) (*hookRpc.CatchRodRouteRsp, error) {
	if req == nil {
		logrus.Errorf("req is nil")
		return nil, nil
	}

	hookRpcCli := crpcHook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, fmt.Errorf("hook rpc client is nil")
	}

	rsp, err := hookRpcCli.GetCatchRodReq(ctx, req)

	if err != nil {
		logrus.Errorf("rpc get err : %v", err)
		return nil, err
	}

	return rsp, nil
}