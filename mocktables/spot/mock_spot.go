package mockSpot

import (
	"math/rand"

	fisherData "chaossrv/internal/robot/fisher_data"
	hallData "chaossrv/internal/robot/hall_data"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"github.com/sirupsen/logrus"
)

// 钓点服情景请求
func GetSpotSceneReq(hallData *hallData.HallData) *spotPB.GetSpotSceneReq {
	if hallData != nil {
		return &spotPB.GetSpotSceneReq{
			PondId: hallData.RoomInfo.GetPondId(),
			SpotId: 1,
			RoomId:  hallData.RoomInfo.GetRoomId(),
		} 
	}

	return nil
}

// ThrowRodReq 抛竿请求
func ThrowRodReq() *spotPB.ThrowRodReq {
	return &spotPB.ThrowRodReq{
		PondId: fisherData.FishSpotId,
		RigId:  1,
		GridInfo: &commonPB.ThrowGridInfo{
			AreaType:     1,
			ObstacleType: 1,
			Pos: &commonPB.Position{
				PosX: 5,
				PosY: 5,
				PosZ: 10,
			},
		},
		HookHabit: &commonPB.HookHabitParam{
			WaterTemp: 230,
			LayerList: []commonPB.MAP_WATER_LAYER_TYPE{1, 2},
			StructureList: []commonPB.UNDER_WATER_STRUCTURE{0, 1, 2},
			BaitPoseInfo: &commonPB.HookBaitTypePose{
				PoseType: commonPB.FISHING_BAIT_TRICK_TYPE_FBTT_TWITCHING,
				Score: 80,
			},
		},
	}
}

// FishHookReq 中鱼请求
func FishHookReq() *spotPB.FishHookReq {
	return &spotPB.FishHookReq{
		PondId: fisherData.FishSpotId,
		RigId:  1,
		GridInfo: &commonPB.ThrowGridInfo{
			AreaType:     2,
			ObstacleType: 3,
			Pos: &commonPB.Position{
				PosX: 5,
				PosY: 5,
				PosZ: 10,
			},
		},
		HookHabit: &commonPB.HookHabitParam{
			WaterTemp: 230,
			LayerList: []commonPB.MAP_WATER_LAYER_TYPE{1, 2},
			StructureList: []commonPB.UNDER_WATER_STRUCTURE{0, 1, 2},
			BaitPoseInfo: &commonPB.HookBaitTypePose{
				PoseType: commonPB.FISHING_BAIT_TRICK_TYPE_FBTT_SHAKING,
				Score: 80,
			},
		},
	}
}

// CatchRodReq 收竿请求
func CatchRodReq() *spotPB.CatchRodReq {
	return &spotPB.CatchRodReq{
		PondId: fisherData.FishSpotId,
		RigId:  1,
		HookHabit: &commonPB.HookHabitParam{
			WaterTemp: 230,
			LayerList: []commonPB.MAP_WATER_LAYER_TYPE{1, 2},
			StructureList: []commonPB.UNDER_WATER_STRUCTURE{0, 1, 2},
			BaitPoseInfo: &commonPB.HookBaitTypePose{
				PoseType: commonPB.FISHING_BAIT_TRICK_TYPE_FBTT_SHAKING,
				Score: 80,
			},
		},
	}
}

// FishEntryOptReq 入护操作请求
func FishEntryOptReq(fishInstance string) *spotPB.FishEntryOptReq {
	if fishInstance == "" {
		logrus.Errorf(">> 参数错误")
		return nil
	}

	return &spotPB.FishEntryOptReq{
		FishInstance: fishInstance,
		Action: commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP,
	}
}

// FishKeepnetOptReq 鱼护操作请求
func FishKeepnetOptReq(instanceList []string) *spotPB.FishKeepnetOptReq {
	if len(instanceList) == 0 {
		return nil
	}

	// 随机一条鱼 
	index := 0
	if len(instanceList) > 1 {
		index = rand.Intn(len(instanceList))
	}

	return &spotPB.FishKeepnetOptReq{
		FishInstance: instanceList[index],
		Action: commonPB.FISH_KEEPNET_OPT_TYPE_FKOT_DISCARD,
	}
}

// KeepnetFishInfoReq 鱼护信息请求
func KeepnetFishInfoReq() *spotPB.KeepnetFishInfoReq {
	return &spotPB.KeepnetFishInfoReq{}
}

// GetRoomPlayerInfoReq 获取房间玩家信息请求
func GetRoomPlayerInfoReq() *spotPB.GetRoomAllPlayerInfoReq {
	return &spotPB.GetRoomAllPlayerInfoReq{}
}

// ExitRoomReq 退出房间请求
func ExitRoomReq() *spotPB.ExitRoomReq {
	return &spotPB.ExitRoomReq{}
}

// ChooseSpotReq 选择钓点
func ChooseSpotReq() *spotPB.ChooseSpotReq {
	return &spotPB.ChooseSpotReq{
		SpotId: 1,
	}
}

// FishBattleReq 搏鱼请求
func FishBattleReq() *spotPB.FishBattleReq {
	return &spotPB.FishBattleReq{
		RigId: 1,
		Result: commonPB.FISH_RESULT_FR_HOOKED,
	}
}

// UseFoodItemReq 使用食物道具请求
func UseFoodItemReq() *hallPB.UseItemReq {
	return &hallPB.UseItemReq{
		SrcType: commonPB.ITEM_SOURCE_TYPE_IST_POND_USE_ITEM,
		ItemInfo: []*commonPB.ItemBase{
			{
				ItemId:    2020005,
				ItemCount: 3,
			},
		},
	}
}

// HookStartReq 请求开始中鱼
func HookStartReq() *spotPB.HookStartReq {
	return &spotPB.HookStartReq{
		PondId: fisherData.FishSpotId,
		RigId:  1,
		CalcType: commonPB.HOOK_FISH_CALC_TYPE_HFCT_START,
		HookHabit: &commonPB.HookHabitParam{
			WaterTemp: 230,
			LayerList: []commonPB.MAP_WATER_LAYER_TYPE{1, 2},
			StructureList: []commonPB.UNDER_WATER_STRUCTURE{0, 1, 2},
			BaitPoseInfo: &commonPB.HookBaitTypePose{
				PoseType: commonPB.FISHING_BAIT_TRICK_TYPE_FBTT_TWITCHING,
				Score: 80,
			},
		},
	}
}
