package mocktables

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	appInfoPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/http"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func AppUpdate() *appInfoPB.AppInfoRsp {
	// RESTApi
	// 默认使用http
	// url := viper.GetString("st_gateway") + "/webapi/getAppInfo"
	apiAdrr := viper.GetString("st_web_api")
	url := "http://" + apiAdrr + "/webapi/getAppInfo"

	req := &appInfoPB.AppInfoReq{
		AppVersion:  "0.0.1",
		AppLanguage: "en",
		ProductId:   commonPB.PRODUCT_ID_PID_FISHER,
		Channel:     commonPB.CHANNEL_TYPE_CT_MASTER,
		Platform:    commonPB.PLATFORM_TYPE_PT_WINDOWS,
	}

	reqBytes, err := proto.Marshal(req)
	if err != nil {
		logrus.Errorf("[Chaos] req.Marshal err : %v", err)
		return nil
	}

	rspBytes, errPost := httpx.PostBytes(context.Background(), url, reqBytes)
	if errPost != nil {
		logrus.Errorln(errPost)
	}

	rsp := &appInfoPB.AppInfoRsp{}
	if errG := proto.UnmarshalMerge(rspBytes, rsp); errG != nil {
		logrus.Errorf("[Chaos] Post err %v", errG)
		return nil
	}
	return rsp
}

func Login() *loginPB.LoginReq {
	return &loginPB.LoginReq{
		LoginType:     commonPB.LOGIN_TYPE_LT_VISITOR,
		ClientVersion: "0.0.1",
		ProductId:     dict.SysDefaultProductID,
		ChannelId:     dict.SysDefaultChannelID,
		DeviceInfo: &commonPB.DeviceInfo{
			DeviceModel: "pc monitor",
			DeviceBrand: "macbook pro",
			Os:          "max osx",
			OsLanguage:  "zh",
			AppLanguage: commonPB.LANGUAGE_TYPE_LT_ZH_CN,
			Resolution:  "1920x1080",
			AdjustId:    "",
			Idfa:        "",
			TimeZone:    "",
			DeviceName:  "mac",
			DeviceCode:  "asdfgghjhjj",
		},
		// AccountInfo: &commonPB.AccountInfo{
		// 	Account:  "test11",
		// 	Password: "123456",
		// },
		// ThirdInfo: &commonPB.ThirdLoginInfo{
		// 	OpenId: "123456",
		// },
		// IsReg:      true,
		ThirdToken: "",
		// AdjustId:   "",
		Network:    commonPB.NETWORK_TYPE_NT_LOCAL,
		BundleName: "fisher.fancy.xyz",
		Platform:   commonPB.PLATFORM_TYPE_PT_WINDOWS,
	}
}

func LoginAccount() *loginPB.LoginReq {
	return &loginPB.LoginReq{
		LoginType:     commonPB.LOGIN_TYPE_LT_VISITOR,
		ClientVersion: "0.0.1",
		ProductId:     dict.SysDefaultProductID,
		ChannelId:     dict.SysDefaultChannelID,
		DeviceInfo: &commonPB.DeviceInfo{
			DeviceModel: "pc monitor",
			DeviceBrand: "macbook pro",
			Os:          "max osx",
			OsLanguage:  "zh",
			AppLanguage: commonPB.LANGUAGE_TYPE_LT_ZH_CN,
			Resolution:  "1920x1080",
			AdjustId:    "",
			Idfa:        "",
			TimeZone:    "",
			DeviceName:  "mac",
			DeviceCode:  "asdfgghjhjj",
		},
		AccountInfo: &commonPB.AccountInfo{
			Account:  "genet666",
			Password: "Genet666",
		},
		ThirdToken: "",
		// AdjustId:   "",
		Network:    commonPB.NETWORK_TYPE_NT_LOCAL,
		BundleName: "fisher.fancy.xyz",
		Platform:   commonPB.PLATFORM_TYPE_PT_WINDOWS,
		IsReg:      false,
	}
}
func LoginToken(token string) *loginPB.LoginReq {
	return &loginPB.LoginReq{
		LoginType:     commonPB.LOGIN_TYPE_LT_TOKEN,
		ClientVersion: "0.0.1",
		ProductId:     dict.SysDefaultProductID,
		ChannelId:     dict.SysDefaultChannelID,
		DeviceInfo: &commonPB.DeviceInfo{
			DeviceModel: "pc monitor",
			DeviceBrand: "macbook pro",
			Os:          "max osx",
			OsLanguage:  "zh",
			AppLanguage: commonPB.LANGUAGE_TYPE_LT_ZH_CN,
			Resolution:  "1920x1080",
			AdjustId:    "",
			Idfa:        "",
			TimeZone:    "",
			DeviceName:  "mac",
			DeviceCode:  "asdfgghjhjj",
		},
		AccountInfo: &commonPB.AccountInfo{
			Account: "test111",
		},
		ThirdToken: token,
		Network:    commonPB.NETWORK_TYPE_NT_LOCAL,
		BundleName: "fisher.fancy.xyz",
		Platform:   commonPB.PLATFORM_TYPE_PT_WINDOWS,
	}
}

func Logout(playerID uint64) *loginPB.LogoutReq {
	return &loginPB.LogoutReq{PlayerId: playerID}
}

func DeleteAccount(productId uint64) *loginPB.DeleteAccountReq {
	return &loginPB.DeleteAccountReq{ProductId: 1}
}
