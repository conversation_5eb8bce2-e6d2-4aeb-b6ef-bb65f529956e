package gm

import (
	"encoding/json"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
)

func GetGmOperateItem(playerId uint64) *gmPB.GmOperateReq {
	req := &gmPB.GmCmdOperateItemReq{
		ProductId:     int32(commonPB.PRODUCT_ID_PID_FISHER),
		PlayerId:      playerId,
		ItemOperation: commonPB.ITEM_OPERATION_IO_ADD,
		ItemId:        101,
		ItemCount:     10,
	}
	data, _ := proto.Marshal(req)
	return &gmPB.GmOperateReq{
		Pid:  commonPB.PRODUCT_ID_PID_FISHER,
		Cmd:  commonPB.GM_CMD_GC_HALL_ITEM,
		Data: string(data),
	}
}

func GmClearWeather() *gmPB.GmOperateReq {
	req := &gmPB.GmCmdClearWeatherReq{
		PondId: 101,
	}

	data, err := json.Marshal(req)
	if err != nil {
		logrus.Errorf("gm proto marshal error: %v", err)
	}
	return &gmPB.GmOperateReq{
		Pid:  commonPB.PRODUCT_ID_PID_FISHER,
		Cmd:  commonPB.GM_CMD_GC_CLEAR_WEATHER,
		Data: string(data),
	}
}

func SendMail() *gmPB.GmOperateReq {
	req := `{
  "player_id": 269,
  "product_id": 1,
  "channel": 1001,
  "detail": {
    "brief": {
      "mail_id": 0,
      "title": "测试邮件-系统",
      "icon_url": "",
      "mail_type": 2,
      "create_time": 20,
      "expires_time": 20,
      "content": "Hello3"
    },
    "read_yet": 0,
    "attach_status": 0,
    "rewards": [
      {
        "loots": [
          {
            "item": {
              "item_id": 1,
              "item_category": 0,
              "item_type": 0,
              "item_sub_type": 0,
              "item_level": 0,
              "item_expire_time": 20,
              "instanceId": "40732880-0640-4161-96f9-a7284c84df6c",
              "update_time": 20
            },
            "value": 20
          }
        ],
        "source": 0
      }
    ]
  }
}`
	data, _ := json.Marshal(req)

	return &gmPB.GmOperateReq{
		Pid: commonPB.PRODUCT_ID_PID_FISHER,
		//Cmd:  commonPB.GM_CMD_GM_CMD_GC_SEND_MSG,
		Data: string(data),
	}
}
