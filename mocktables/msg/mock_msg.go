package mockMsg

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	mailPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/msg"
)


func MailListReq() *mailPB.GetMailListReq {
	return &mailPB.GetMailListReq{
		PageIndex: 1,
		MailType: commonPB.MAIL_TYPE_MT_SYSTEM,
	}
}

func ReadMailReq() *mailPB.ReadMailReq {
	return &mailPB.ReadMailReq{
		MailId: 1,
		MailType: commonPB.MAIL_TYPE_MT_SYSTEM,
	}
}

func ClaimAllMailReq() *mailPB.ClaimRewardAttachReq {
	return &mailPB.ClaimRewardAttachReq{
		MailType: commonPB.MAIL_TYPE_MT_SYSTEM,
		ClaimAll: true,
		MailId: 1,
	}
}