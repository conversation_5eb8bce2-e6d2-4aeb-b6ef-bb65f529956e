package task

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
)

func GetTaskList(category commonPB.TASK_CATEGORY) *taskPB.GetTaskListReq {
	return &taskPB.GetTaskListReq{
		Category: category,
		} 
	}

func GetTaskProgress(category commonPB.TASK_CATEGORY ) *taskPB.TaskProgressReq {
	return &taskPB.TaskProgressReq{
		Category: category,
	}
}

func RewardTask(category commonPB.TASK_CATEGORY, taskID int64) *taskPB.RewardTaskReq {
	return &taskPB.RewardTaskReq{
		Category: category,
		TaskId: taskID,
	}
}

func RewardProgressTask(category commonPB.TASK_CATEGORY, subId int64) *taskPB.TaskProgressRewardReq {
	return &taskPB.TaskProgressRewardReq{
		Category: category,
		SubId: subId,
		Index: 20133007,
	}
}