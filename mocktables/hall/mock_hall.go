package mockHall

import (
	fisherData "chaossrv/internal/robot/fisher_data"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
)

// GetRoomInfoReq 获取玩家房间信息请求 proto
func GetRoomInfoReq() *hallPB.GetRoomInfoReq {
	return &hallPB.GetRoomInfoReq{}
}

// EnterFisheryReq 进入钓点请求 proto
func EnterFisheryReq() *hallPB.EnterFisheryReq {
	return &hallPB.EnterFisheryReq{
		PondId:   fisherData.FishSpotId,
		GameType: commonPB.GAME_TYPE_GT_RECREATION,
		RoomType: commonPB.ROOM_TYPE_RT_CASUAL,
	}
}

// GetItemInfoReq 查询道具id列表请求
func GetItemInfoReq() *hallPB.GetItemInfoReq {
	return &hallPB.GetItemInfoReq{
		ItemList: []int64{101, 102, 301},
	}
}

// GetItemInfoByType 根据类型查询道具信息请求
func GetItemInfoByType() *hallPB.GetItemInfoByTypeReq {
	return &hallPB.GetItemInfoByTypeReq{
		ItemType: -1,
	}
}

// GetGoodsBuyInfoReq 商品购买信息请求
func GetGoodsBuyInfoReq() *hallPB.GetGoodsBuyInfoReq {
	return &hallPB.GetGoodsBuyInfoReq{
		GoodsList: []int64{101010002, 109010003},
	}
}

// StoreBuyReq 商城购买请求
func StoreBuyReq() *hallPB.StoreBuyReq {
	return &hallPB.StoreBuyReq{
		StoreBuyId: 5010006,
		Count:      1,
		StyleType:  commonPB.STORE_SHOW_STYLE_STRT_ROOM,
	}
}

// GetLastGameInfoReq 上次游戏信息
func GetLastGameInfoReq() *hallPB.GetLastGameInfoReq {
	return &hallPB.GetLastGameInfoReq{}
}

// GetPlayerInfoReq 获取玩家信息
func GetPlayerInfoReq() *hallPB.GetPlayerInfoReq {
	return &hallPB.GetPlayerInfoReq{
		PlayerId: 179,
	}
}

// UseItemReq 消耗道具请求
func UseItemReq() *hallPB.UseItemReq {
	return &hallPB.UseItemReq{
		SrcType: commonPB.ITEM_SOURCE_TYPE_IST_POND_USE_ITEM,
		ItemInfo: []*commonPB.ItemBase{
			{
				ItemId:    2020001,
				ItemCount: 2,
			},
		},
	}
}

// FirstEnterHallReq 首次进入大厅请求
func FirstEnterHallReq() *hallPB.FirstEnterHallReq {
	return &hallPB.FirstEnterHallReq{}
}

// GetStatsListReq 获取玩家统计信息请求
func GetStatsListReq(player uint64) *hallPB.GetStatListReq {
	return &hallPB.GetStatListReq{}
}

// UpdateRodRigInfoReq 更新竿架信息请求
func UpdateRodRigInfoReq() *hallPB.UpdateRodRigInfoReq {
	return &hallPB.UpdateRodRigInfoReq{
		RigInfo: &commonPB.RodRigInfo{
			RigId:    1,
			Name:     "竿组1",
			RodId:    3012001,
			ReelId:   3021001,
			LineId:   3034001,
			LeaderId: 0,
			BaitId:   309201,
			FloatId:  0,
			HookId:   3081003,
		},
	}
}

// GetRodRigInfoReq 查询干架信息请求
func GetRodRigInfoReq() *hallPB.GetRodRigInfoReq {
	return &hallPB.GetRodRigInfoReq{
		RigId: -1,
	}
}

// DeleteRodRigInfoReq 删除干架信息
func DeleteRodRigInfoReq() *hallPB.DeleteRodRigInfoReq {
	return &hallPB.DeleteRodRigInfoReq{
		RigId: 1,
	}
}

// GetTripBag 查询旅行背包
func GetTripBag1(playerId uint64) *hallPB.GetTripBagReq {
	return &hallPB.GetTripBagReq{
		Type: 1,
	}
}

func GetTripBag2(playerId uint64) *hallPB.GetTripBagReq {
	return &hallPB.GetTripBagReq{
		Type: 2,
	}
}

// ModifyTripBagReq 修改旅行背包
func ModifyTripBagReq1(playerId uint64) *hallPB.ModifyTripBagReq {
	return &hallPB.ModifyTripBagReq{
		Type:    commonPB.TRIP_BAG_TYPE_TBT_FOOD,
		Operate: commonPB.TRIP_BAG_OPERATE_TBO_IN,
		ItemId:  2020001,
		Count:   2,
	}
}

func ModifyTripBagReq2(playerId uint64) *hallPB.ModifyTripBagReq {
	return &hallPB.ModifyTripBagReq{
		Type:    commonPB.TRIP_BAG_TYPE_TBT_FOOD,
		Operate: commonPB.TRIP_BAG_OPERATE_TBO_OUT,
		ItemId:  2020001,
		Count:   2,
	}
}

func GetTripRodReq(playerId uint64) *hallPB.GetTripRodReq {
	return &hallPB.GetTripRodReq{}
}

func LoadTripRodReq(rigId int32, id int32) *hallPB.LoadTripRodReq {
	return &hallPB.LoadTripRodReq{
		RigId: rigId,
		Id:    id,
	}
}

func LoadTripRodReq2(playerId uint64) *hallPB.LoadTripRodReq {
	return &hallPB.LoadTripRodReq{
		RigId: 1,
		Id:    2,
	}
}

func DelTripRodReq(playerId uint64) *hallPB.DelTripRodReq {
	return &hallPB.DelTripRodReq{
		Id: 1,
	}
}

func UpdateRodReq(playerId uint64) *hallPB.UpdateTripRodReq {
	return &hallPB.UpdateTripRodReq{
		Id:         1,
		Sit:        1,
		ItemId:     1,
		InstanceId: "iii",
	}
}

// 实名认证
func RealNameAuth(playerId uint64) *hallPB.RealNameAuthReq {
	return &hallPB.RealNameAuthReq{
		RealName:  "陈君健",
		IdCardNum: "******************",
	}
}

// 新手引导进度
func UpdateGuideProgressReq() *hallPB.UpdateGuideProgressReq {
	return &hallPB.UpdateGuideProgressReq{
		Progress: 2,
	}
}
